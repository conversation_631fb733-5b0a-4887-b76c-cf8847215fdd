'use strict';

const { OrderStatus } = require('../../../utils/constants');

/**
 * dashboard service
 */

/**
 *
 * Start for summary
 */
const getSumCommission = (userId) => {
  return strapi.db.connection.context.raw(
    `SELECT SUM(od.total_commission)
            FROM orders as od 
            INNER JOIN up_users as u ON od.creator_id = u.id
            WHERE (u.id = ${userId} OR u.f_parent = ${userId})
            AND (od.order_status = ${OrderStatus.Success}
            OR od.order_status = ${OrderStatus.Pending}
            OR od.order_status = ${OrderStatus.Shipping}
            OR od.order_status = ${OrderStatus.Confirmed})
        `
  );
};
const getTotalCommissionPending = async (userId) => {
  const res = await strapi.db.connection.context.raw(
    `SELECT SUM(od.total_commission)
            FROM orders as od 
            INNER JOIN up_users as u ON od.creator_id = u.id
            WHERE (u.id = ${userId} OR u.f_parent = ${userId})
            AND od.order_status = ${OrderStatus.Pending}
        `
  );
  return res;
};

const getTotalCommissionSuccess = async (userId) => {
  const res = await strapi.db.connection.context.raw(
    `SELECT SUM(od.total_commission)
            FROM orders as od 
            INNER JOIN up_users as u ON od.creator_id = u.id
            WHERE (u.id = ${userId} OR u.f_parent = ${userId})
            AND od.order_status = ${OrderStatus.Success}
        `
  );
  return res;
};

const getTotalOrder = async (userId) => {
  return strapi.db.query('api::order.order').count({
    where: {
      creator: userId,
    },
  });
};

const getSumUserRevenue = async (userId) => {
  return strapi.db.connection.context.raw(
    `SELECT SUM(od.final_price)
            FROM orders as od 
            WHERE od.creator_id = ${userId}
          `
  );
};

const getSumTeamRevenue = async (userId) => {
  return strapi.db.connection.context.raw(
    `SELECT SUM(od.final_price)
            FROM orders as od 
            INNER JOIN up_users as u ON od.creator_id = u.id
            WHERE (u.id = ${userId} OR u.f_parent = ${userId})
        `
  );
};

const getTotalOrderSuccess = async (userId) => {
  return strapi.db.query('api::order.order').count({
    where: {
      orderStatus: OrderStatus.Success,
      creator: userId,
    },
  });
};

const getTotalOrderShipping = async (userId) => {
  return strapi.db.query('api::order.order').count({
    where: {
      orderStatus: OrderStatus.Shipping,
      creator: userId,
    },
  });
};

/**
 *
 * End for summary
 */

/**
 *
 * Start for summary business
 */
const getCountOrderSuccess = async (userId, filters) => {
  const startDate = filters?.['$and'].find(
    (item) => item?.['createdAt']?.['$gte']
  )?.['createdAt']?.['$gte'];
  const endDate = filters?.['$and'].find(
    (item) => item?.['createdAt']?.['$lte']
  )?.['createdAt']?.['$lte'];

  return strapi.db.connection.context.raw(
    `SELECT COUNT(od.final_price), CAST(od.created_at AS DATE)
                FROM orders as od 
                INNER JOIN up_users as u ON od.creator_id = u.id
                WHERE (u.id = ${userId} OR u.f_parent = ${userId})
                ${!startDate ? '' : `AND od.created_at >= '${startDate}'`}
                ${!endDate ? '' : `AND od.created_at <= '${endDate}'`}
                GROUP BY CAST(od.created_at AS DATE)
            `
  );
};

const getCountCommission = (userId, filters) => {
  const startDate = filters?.['$and'].find(
    (item) => item?.['createdAt']?.['$gte']
  )?.['createdAt']?.['$gte'];
  const endDate = filters?.['$and'].find(
    (item) => item?.['createdAt']?.['$lte']
  )?.['createdAt']?.['$lte'];

  return strapi.db.connection.context.raw(
    `SELECT COUNT(od.total_commission), CAST(od.created_at AS DATE)
              FROM orders as od 
              INNER JOIN up_users as u ON od.creator_id = u.id
              WHERE (u.id = ${userId} OR u.f_parent = ${userId})
              AND (od.order_status = ${OrderStatus.Success}
              OR od.order_status = ${OrderStatus.Pending}
              OR od.order_status = ${OrderStatus.Shipping}
              OR od.order_status = ${OrderStatus.Confirmed})
              ${!startDate ? '' : `AND od.created_at >= '${startDate}'`}
              ${!endDate ? '' : `AND od.created_at <= '${endDate}'`}
              GROUP BY CAST(od.created_at AS DATE)
          `
  );
};

const getCountCommissionSuccess = async (userId, filters) => {
  const startDate = filters?.['$and'].find(
    (item) => item?.['createdAt']?.['$gte']
  )?.['createdAt']?.['$gte'];
  const endDate = filters?.['$and'].find(
    (item) => item?.['createdAt']?.['$lte']
  )?.['createdAt']?.['$lte'];

  return strapi.db.connection.context.raw(
    `SELECT COUNT(od.total_commission), CAST(od.created_at AS DATE) 
            FROM orders as od 
            INNER JOIN up_users as u ON od.creator_id = u.id
            WHERE (u.id = ${userId} OR u.f_parent = ${userId})
            AND od.order_status = ${OrderStatus.Success}
            ${!startDate ? '' : `AND od.created_at >= '${startDate}'`}
            ${!endDate ? '' : `AND od.created_at <= '${endDate}'`}
            GROUP BY CAST(od.created_at AS DATE)
        `
  );
};

const getCountOrder = async (userId, filters) => {
  const startDate = filters?.['$and'].find(
    (item) => item?.['createdAt']?.['$gte']
  )?.['createdAt']?.['$gte'];
  const endDate = filters?.['$and'].find(
    (item) => item?.['createdAt']?.['$lte']
  )?.['createdAt']?.['$lte'];

  return strapi.db.connection.context.raw(
    `SELECT COUNT(od.final_price), CAST(od.created_at AS DATE)
            FROM orders as od 
            WHERE od.creator_id = ${userId}
            ${!startDate ? '' : `AND od.created_at >= '${startDate}'`}
            ${!endDate ? '' : `AND od.created_at <= '${endDate}'`}
            GROUP BY CAST(od.created_at AS DATE)
    `
  );
};

const getCountUserRevenue = async (userId, filters) => {
  const startDate = filters?.['$and'].find(
    (item) => item?.['createdAt']?.['$gte']
  )?.['createdAt']?.['$gte'];
  const endDate = filters?.['$and'].find(
    (item) => item?.['createdAt']?.['$lte']
  )?.['createdAt']?.['$lte'];

  return strapi.db.connection.context.raw(
    `SELECT SUM(od.final_price), CAST(od.created_at AS DATE)
            FROM orders as od 
            WHERE od.creator_id = ${userId}
            ${!startDate ? '' : `AND od.created_at >= '${startDate}'`}
            ${!endDate ? '' : `AND od.created_at <= '${endDate}'`}
            GROUP BY CAST(od.created_at AS DATE)
          `
  );
};

const getCountTeamRevenue = async (userId, filters) => {
  const startDate = filters?.['$and'].find(
    (item) => item?.['createdAt']?.['$gte']
  )?.['createdAt']?.['$gte'];
  const endDate = filters?.['$and'].find(
    (item) => item?.['createdAt']?.['$lte']
  )?.['createdAt']?.['$lte'];

  return strapi.db.connection.context.raw(
    `SELECT SUM(od.final_price), CAST(od.created_at AS DATE)
              FROM orders as od 
              INNER JOIN up_users as u ON od.creator_id = u.id
              WHERE (u.id = ${userId} OR u.f_parent = ${userId})
              ${!startDate ? '' : `AND od.created_at >= '${startDate}'`}
              ${!endDate ? '' : `AND od.created_at <= '${endDate}'`}
              GROUP BY CAST(od.created_at AS DATE)
          `
  );
};

/**
 *
 * End for summary business
 */

module.exports = () => ({
  summary: async (user, query, params) => {
    try {
      const { balance } = user;

      const [
        totalOrderShipping,
        totalOrderSuccess,
        totalCommission,
        totalCommissionPending,
        totalCommissionSuccess,
        totalOrder,
        totalUserRevenue,
        totalTeamRevenue,
      ] = await Promise.all([
        getTotalOrderShipping(user.id),
        getTotalOrderSuccess(user.id),
        getSumCommission(user.id),
        getTotalCommissionPending(user.id),
        getTotalCommissionSuccess(user.id),
        getTotalOrder(user.id),
        getSumUserRevenue(user.id),
        getSumTeamRevenue(user.id),
      ]);

      return {
        balance, // Số dư ví
        totalCommission: totalCommission?.rows?.[0]?.['sum'] || 0, // Hoa hồng tạm tính
        totalCommissionPending: totalCommissionPending?.rows?.[0]?.['sum'] || 0, // Hoa hồng chờ duyệt
        totalCommissionSuccess: totalCommissionSuccess?.rows?.[0]?.['sum'] || 0, // Hoa hồng đã duyệt
        totalOrderSuccess, // Số đơn hàng thành công
        totalOrderShipping, // Số đơn hàng đang giao
        totalUserRevenue: totalUserRevenue?.rows?.[0]?.['sum'] || 0, // Doanh thu cá nhân
        totalOrder, // Đơn hàng phát sinh
        totalTeamRevenue: totalTeamRevenue?.rows?.[0]?.['sum'] || 0, // Doanh thu nhóm
      };
    } catch (error) {
      console.log(':error: ', error);
    }
  },

  summaryBusiness: async (user, query, params) => {
    try {
      const { filters } = query;

      const [
        orderSuccess,
        commission,
        commissionSuccess,
        order,
        userRevenue,
        teamRevenue,
      ] = await Promise.all([
        getCountOrderSuccess(user.id, filters),
        getCountCommission(user.id, filters),
        getCountCommissionSuccess(user.id, filters),
        getCountOrder(user.id, filters),
        getCountUserRevenue(user.id, filters),
        getCountTeamRevenue(user.id, filters),
      ]);

      return {
        commission: commission?.rows || [], // Hoa hồng tạm tính
        commissionSuccess: commissionSuccess?.rows || [], // Hoa hồng đã duyệt
        orderSuccess: orderSuccess?.rows || [], // Đơn hàng thành công
        userRevenue: userRevenue?.rows || [], // Doanh thu cá nhân
        order: order?.rows || [], // Đơn hàng phát sinh
        teamRevenue: teamRevenue?.rows || [], // Doanh thu nhóm
      };
    } catch (error) {
      console.log(':error: ', error);
    }
  },

  ranking: async () => {
    try {
      const res = await strapi.db.connection.context.raw(
        `SELECT SUM(od.final_price) as money, od.creator_id, MAX(u.phone) AS phone, Max(u.name) AS name
                FROM orders as od 
                INNER JOIN up_users as u ON od.creator_id = u.id
                GROUP BY od.creator_id
                ORDER BY money DESC
                LIMIT 10
              `
      );
      const userIds = res?.rows?.map((item) => item?.creator_id);

      const users = await strapi.db
        .query('plugin::users-permissions.user')
        .findMany({
          where: {
            id: {
              $in: userIds,
            },
          },
          populate: ['avatar'],
        });
      const result = userIds.map((userId) => {
        const user = users.find((item) => item.id === userId);
        const item = res?.rows.find((row) => row?.creator_id === userId);
        return {
          ...item,
          avatar: user?.avatar?.url,
        };
      });
      return result || [];
    } catch (error) {
      console.log('error: ', error);
    }
  },
});
