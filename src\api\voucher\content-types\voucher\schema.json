{"kind": "collectionType", "collectionName": "vouchers", "info": {"singularName": "voucher", "pluralName": "vouchers", "displayName": "Voucher", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"code": {"type": "string", "required": true}, "type": {"type": "enumeration", "enum": ["freeship", "discountPercent", "discountPrice"], "required": true}, "quantity": {"type": "integer", "required": true}, "startDate": {"type": "datetime", "required": true}, "expiredDate": {"type": "datetime"}, "status": {"type": "enumeration", "enum": ["active", "inactive"]}, "discountValue": {"type": "integer"}, "maxPrice": {"type": "integer"}, "minPrice": {"type": "integer"}, "matchPoint": {"type": "integer", "required": true}, "content": {"type": "text"}, "users": {"type": "relation", "relation": "manyToMany", "target": "plugin::users-permissions.user", "mappedBy": "vouchers"}, "banner": {"allowedTypes": ["images", "files", "videos", "audios"], "type": "media", "multiple": false}}}