'use strict';
const utils = require('@strapi/utils');
const {
  OrderStatus,
  BonusPercent,
  MinRevenueForLevel,
  Level,
  BonusPercentForChild,
  REFER_CODE_ROOT,
  NewUserRegisterCommissionValue,
  TransactionType,
} = require('../../../utils/constants');
const {
  convertPagedToStartLimit,
} = require('@strapi/strapi/lib/core-api/service/pagination');
const {
  getAllChildrenOf,
  queryUserById,
  filterPaidOrder,
  trackEvent,
  snakeToCamel,
} = require('../../../utils/utils');
const dayjs = require('dayjs');
const { ApplicationError, ValidationError } = utils.errors;

/**
 * my-team service
 */
const successStatuses = [
  OrderStatus.Confirmed,
  OrderStatus.Shipping,
  OrderStatus.Success,
];
const allStatuses = [OrderStatus.Pending, ...successStatuses];

const getStatusText = (statuses) => {
  return statuses.map((s) => `od.order_status = ${s}`).join(' OR ');
};

const MaxLevelQuery = 2;

const findSalesOfUser = async (userId) => {
  const res = await strapi.entityService.findMany('api::order.order', {
    filters: {
      creator: userId,
      status: OrderStatus.Success,
    },
  });
  return res.reduce((acc, item) => acc + Number(item.finalPrice), 0);
};

const getChildren = async (output, userId, query, level) => {
  // if (level >= MaxLevelQuery) return;
  const users = await strapi.query('plugin::users-permissions.user').findMany({
    where: {
      ...query,
      fParent: userId,
      blocked: false,
    },
  });
  const children = [];
  for (const user of users) {
    delete user?.password;
    delete user?.resetPasswordToken;
    delete user?.confirmationToken;

    children.push({
      ...user,
      fLevel: level + 1,
    });
  }
  children.length !== 0 && output.push(...children);
  for (const child of children) {
    await getChildren(output, child.id, query, level + 1);
  }
};

const getChildrenByLevel = async (userId, query, level) => {
  // if (level >= MaxLevelQuery) return;
  const result = [];
  const users = await strapi.query('plugin::users-permissions.user').findMany({
    where: {
      ...query,
      fParent: userId,
      blocked: false,
    },
  });

  for (const user of users) {
    delete user?.password;
    delete user?.resetPasswordToken;
    delete user?.confirmationToken;
    const children = await getChildrenByLevel(user.id, query, level + 1);
    const totalCommission = await getTotalCommissionByUserId(user.id);
    result.push({
      ...user,
      id: String(user.id),
      label: user.phone,
      fLevel: level + 1,
      children: children,
      totalCommission,
    });
  }
  return result;
};

/**
 * my-team service
 */
const calcUserLevel = async (userId) => {
  const user = await queryUserById(userId);
  const level = parseInt(user.level);
  if (
    !user ||
    !level ||
    level < Level.BronzeMember ||
    level >= Level.DiamondMember
  )
    return null;
  const now = dayjs();
  const month = now.month() + 1;
  const year = now.year();
  const userSaleInMonth = await strapi.db.query('api::sale.sale').findOne({
    where: {
      userId: user.id,
      month: month,
      year: year,
    },
  });
  const totalSale = userSaleInMonth ? parseInt(userSaleInMonth.sale) : 0;
  return {
    nextLevel: level + 1,
    curLevel: level,
    curLevelValue: totalSale,
    nextLevelValue: MinRevenueForLevel[user.level + 1],
  };
};

const getMember = async (parentId, query, level) => {
  const children = [];
  await getChildren(children, parentId, query, level);
  return children;
};

const getTotalCommissionByUserId = async (userId) => {
  let totalManagementCommissionValue = 0;
  const userParent = await queryUserById(userId);
  const userParentLevel = parseInt(userParent?.level);
  const userParentSale = parseInt(userParent?.mySale) || 0;
  if (userParentLevel && userParentLevel > Level.NewUser) {
    totalManagementCommissionValue += await calcManagementCommission(
      userParentLevel,
      userId
    );
  }
  const rateCommissionPartner =
    parseFloat(userParent.rateCommissionPartner) / 100;
  const totalMyCommission = rateCommissionPartner
    ? Math.round(userParentSale * rateCommissionPartner)
    : userParentSale * BonusPercent[userParentLevel];
  const totalFixedCommission = await calculateFixedCommission(userId);
  return {
    totalManagementCommissionValue,
    totalMyCommission,
    totalFixedCommission,
  };
};
const calcManagementCommission = async (parentLevel, parentId) => {
  let total = 0;
  const users = await strapi.query('plugin::users-permissions.user').findMany({
    where: {
      fParent: parentId,
      blocked: false,
    },
  });
  for (const user of users) {
    const userSale = parseInt(user.mySale) || 0;
    const myTeamSale = parseInt(user.myTeamSale) || 0;
    total +=
      (userSale + myTeamSale) * BonusPercentForChild[parentLevel][user?.level];
    // total += await calcManagementCommission(parentLevel, user.id);
  }
  return total;
};

const calculateFixedCommission = async (userId) => {
  const orders = await strapi.entityService.findMany('api::order.order', {
    filters: {
      creator: userId,
      ...filterPaidOrder,
    },
  });
  let totalFixedCommission = 0;
  for (const order of orders) {
    const products = order.orderData.items || [];
    for (const productData of products) {
      const { quantity, product } = productData;
      const isFixedCommission = product.isFixedCommission;
      if (isFixedCommission) {
        const commission = product.commission || 0;
        totalFixedCommission += commission * quantity;
      }
    }
  }

  return totalFixedCommission;
};
const getTotalCommission = async (userId) => {
  const totalF1Price = await strapi.db.connection.context.raw(
    `SELECT sum(final_price)
      FROM orders as od
      INNER JOIN orders_creator_links as odrLink ON od.id = odrLink.order_id
      INNER JOIN up_users as u ON odrLink.user_id = u.id
      WHERE u.f_parent = ${userId}
      AND (od.order_status = ${OrderStatus.Success}
      OR od.order_status = ${OrderStatus.Pending}
      OR od.order_status = ${OrderStatus.Shipping}
      OR od.order_status = ${OrderStatus.Confirmed})`
  );
  const userIdsF1 = await strapi.db.connection.context.raw(
    `SELECT id
      FROM up_users
      WHERE f_parent = ${userId}
      `
  );
  const ids =
    userIdsF1.rows.length && userIdsF1.rows.map((item) => item.id).join(',');
  let totalF2Price = 0;
  if (ids) {
    totalF2Price = await strapi.db.connection.context.raw(
      `SELECT sum(final_price)
      FROM orders as od
      INNER JOIN orders_creator_links as odrLink ON od.id = odrLink.order_id
      INNER JOIN up_users as u ON odrLink.user_id = u.id
      WHERE u.f_parent IN (${ids})
      AND (od.order_status = ${OrderStatus.Success}
        OR od.order_status = ${OrderStatus.Pending}
        OR od.order_status = ${OrderStatus.Shipping}
        OR od.order_status = ${OrderStatus.Confirmed})`
    );
  }

  return (
    ((totalF1Price?.rows[0]?.['sum'] || 0) * BonusPercent[0]) / 100 +
    ((totalF2Price?.rows[0]?.['sum'] || 0) * BonusPercent[1]) / 100
  );
};

const getTotalOrderSuccess = (userId) => {
  return strapi.db.query('api::order.order').count({
    where: {
      $or: [{ orderStatus: OrderStatus.Success }],
      creator: userId,
    },
  });
};

const getCommissionIncludePending = async (userId, currentLevel = 0) => {
  if (currentLevel === BonusPercent.length) return;

  const data = await strapi.db.connection.context.raw(
    `SELECT u.id as creator, SUM(od.final_price) as order_price
            FROM orders as od
            INNER JOIN orders_creator_links as odrLink ON od.id = odrLink.order_id
            INNER JOIN up_users as u ON odrLink.user_id = u.id
            WHERE u.f_parent = ${userId}
            AND (${getStatusText(allStatuses)})
            GROUP BY u.id
        `
  );
  let total = data.rows.reduce((acc, item) => {
    return acc + item.order_price * BonusPercent[currentLevel];
  }, 0);
  for (const item of data.rows) {
    total += await getCommissionIncludePending(item.creator, currentLevel + 1);
  }

  return total;
};

const getCommissionSuccess = async (userId, currentLevel = 0) => {
  if (currentLevel === BonusPercent.length) return;

  const data = await strapi.db.connection.context.raw(
    `SELECT u.id as creator, SUM(od.final_price) as order_price
            FROM orders as od
            INNER JOIN orders_creator_links as odrLink ON od.id = odrLink.order_id
            INNER JOIN up_users as u ON odrLink.user_id = u.id
            WHERE u.f_parent = ${userId}
            AND (${getStatusText(successStatuses)})
            GROUP BY u.id
        `
  );
  let total = data.rows.reduce((acc, item) => {
    return acc + item.order_price * BonusPercent[currentLevel];
  }, 0);
  for (const item of data.rows) {
    total += await getCommissionIncludePending(item.creator, currentLevel + 1);
  }

  return total;
};

const getSuccessOrders = async (userId) => {
  const childrenOfUser = [userId];
  await getAllChildrenOf(childrenOfUser, userId);
  const newQuery = {
    filters: {
      creator: childrenOfUser,
    },
  };
  const [, count] = await strapi
    .query('api::order.order')
    .findWithCount(newQuery);
  return count;
};

const getShippingOrders = async (userId) => {
  return strapi.db.query('api::order.order').count({
    where: {
      orderStatus: OrderStatus.Shipping,
      creator: userId,
    },
  });
};

const getTotalOrders = async (userId) => {
  return strapi.db.query('api::order.order').count({
    where: {
      creator: userId,
    },
  });
};

const addReferUser = async (newUser, referUser) => {
  const newChildren = referUser.sChildren
    ? [...referUser.sChildren.split(','), newUser.id]
    : [newUser.id];

  await strapi.db.query('plugin::users-permissions.user').update({
    where: {
      id: referUser.id,
    },
    data: {
      sChildren: newChildren.join(','),
    },
  });
};

const calcPendingCommission = async (referCode) => {
  const parentInfo = await strapi.db
    .query('plugin::users-permissions.user')
    .findOne({
      where: {
        referCode,
      },
    });
  if (!parentInfo) {
    console.log('Cannot find user parent', userParent);
    return;
  }
  await Promise.all([calcPendingCommissionForReferUser(parentInfo)]);
};

const calcPendingCommissionForReferUser = async (parentInfo) => {
  try {
    // let addValue = LevelCommissionValue[parentInfo.level];

    // if ([Level.NewUser, Level.Vip].includes(parentInfo.level)) {
    //   addValue += NewUserRegisterCommissionValue;
    // }
    // if (!addValue) {
    //   console.log(
    //     `payPendingCommissionForReferUser::Parent is ${
    //       LevelName[parentInfo.level]
    //     }. User id:`,
    //     parentInfo.id,
    //     'cannot get commission'
    //   );
    //   return;
    // }

    let addValue = NewUserRegisterCommissionValue;
    let currentCommission = parentInfo.commission;

    let pendingCommissionBefore = currentCommission?.pendingCommission ?? 0;

    currentCommission = {
      ...currentCommission,
      pendingCommission: pendingCommissionBefore + addValue,
    };

    await strapi.db.query('plugin::users-permissions.user').update({
      where: { id: parentInfo.id },
      data: {
        commission: {
          ...currentCommission,
        },
      },
    });

    trackEvent('addPendingCommission', {
      to: parentInfo.id,
      amount: addValue,
      pendingCommissionBefore: pendingCommissionBefore,
      pendingCommissionAfter: currentCommission.pendingCommission,
      type: TransactionType.PendingCommissionRefer,
      moneySource: parentInfo.referCode,
    });
  } catch (error) {
    trackEvent('error', {
      error,
    });
  }
};

module.exports = () => ({
  find: async (userId, query) => {
    try {
      const { pagination } = query;
      delete query.pagination;

      const output = [];
      let result = [];
      let level = 0;
      await getChildren(output, userId, query, level);

      let resPagination = null;
      if (pagination) {
        const { page, pageSize } = pagination;

        result = output.slice((page - 1) * pageSize, page * pageSize);

        resPagination = {
          total: output.length,
          page,
          pageSize: result.length,
        };
      } else {
        result = output;
      }
      return { data: result, pagination: resPagination };
    } catch (err) {
      return err;
    }
  },

  join: async (body, user) => {
    const { referCode } = body;

    const parent = await strapi.db
      .query('plugin::users-permissions.user')
      .findOne({
        where: { referCode },
      });

    if (!parent) {
      throw new ApplicationError('Invalid refer code');
    }
    if (parent.id === user.id) {
      throw new ApplicationError('You cannot join your own team');
    }
    const checkExist = await strapi.db
      .query('plugin::users-permissions.user')
      .findOne({
        where: {
          id: user.id,
          updatedReferCode: true,
        },
      });

    if (checkExist) {
      throw new ApplicationError('User has joined the team');
    }

    await strapi.entityService.update(
      'plugin::users-permissions.user',
      user.id,
      {
        data: {
          fParent: parent.id,
          updatedReferCode: true,
        },
      }
    );

    await addReferUser(user, parent);

    await calcPendingCommission(referCode);

    return {
      message: 'Join team successfully',
    };
  },

  async getMember(userId, query) {
    const searchConditions =
      query && Object.keys(query).length > 0
        ? `WHERE ${Object.keys(query)
            .map((key) => {
              if (query[key]) {
                const value = query[key]['$containsi']
                  ? `%${query[key]['$containsi']}%`
                  : '%';
                return `${key} ILIKE ${strapi.db.connection.raw('?', [value])}`;
              }
              return '';
            })
            .filter((cond) => cond)
            .join(' OR ')}`
        : '';

    const res = await strapi.db.connection.context.raw(
      `WITH RECURSIVE user_tree AS (
            SELECT *, 0 AS f_level
            FROM up_users
            WHERE id = ${userId}

            UNION ALL

            SELECT u.*, ut.f_level + 1
            FROM up_users u
            INNER JOIN user_tree ut ON ut.id = u.f_parent
        ),
        f1_count AS (
            SELECT f_parent, COUNT(*) AS f1_children_count
            FROM up_users
            GROUP BY f_parent
        ),
        descendants AS (
            SELECT id, id AS root_id
            FROM user_tree
            UNION ALL
            SELECT ut.id, d.root_id
            FROM user_tree ut
            JOIN descendants d ON ut.f_parent = d.id
        ),
        descendant_ids AS (
            SELECT root_id, array_agg(id) AS descendant_ids
            FROM descendants
            WHERE id != root_id
            GROUP BY root_id
        )
        SELECT
            ut.*,
            COALESCE(fc.f1_children_count, 0) AS total_branch,
            COALESCE(di.descendant_ids, ARRAY[]::INTEGER[]) AS total_children,
            ut.balance AS total_commission_success
        FROM user_tree ut
        LEFT JOIN f1_count fc ON ut.id = fc.f_parent
        LEFT JOIN descendant_ids di ON ut.id = di.root_id
        ${searchConditions}`
    );

    const data = res.rows
      ?.filter((r) => {
        return r.id !== Number(userId);
      })
      ?.map((r) => {
        const camelRow = Object.keys(r).reduce((data, key) => {
          data[snakeToCamel(key)] = r[key];
          return data;
        }, {});

        delete r.password;
        delete r.resetPasswordToken;
        delete r.confirmationToken;

        return camelRow;
      });

    return { data };
  },

  async getMemberByLevel(userId, query) {
    let level = 0;
    const output = await getChildrenByLevel(userId, query, level);
    return { data: output };
  },

  getOrder: async (userId, query) => {
    const childrenOfUser = [userId];
    await getAllChildrenOf(childrenOfUser, userId);
    const newQuery = {
      ...query,
      ...convertPagedToStartLimit(query),
      filters: {
        ...query.filters,
        creator: childrenOfUser,
      },
    };

    const [data, count] = await strapi
      .query('api::order.order')
      .findWithCount(newQuery);
    return {
      data,
      meta: {
        pagination: {
          page: query?.pagination?.page || 1,
          pageSize: query?.pagination?.pageSize || 10,
          pageCount: Math.ceil(count / (query?.pagination?.pageSize || 10)),
          total: count,
        },
      },
    };
  },

  async report(userId) {
    try {
      const [
        totalCommissionPending,
        totalCommissionSuccess,
        totalOrderSuccess,
        totalOrderShipping,
        totalOrder,
        allMembers,
        userLevelData,
        totalCommissionValue,
      ] = await Promise.all([
        getCommissionIncludePending(userId),
        getCommissionSuccess(userId),
        getSuccessOrders(userId),
        getShippingOrders(userId),
        getTotalOrders(userId),
        getMember(userId),
        calcUserLevel(userId),
        getTotalCommissionByUserId(userId),
      ]);

      return {
        totalCommissionPending, // Hoa hồng chờ duyệt
        totalCommissionSuccess, // Hoa hồng đã duyệt
        totalOrderSuccess, // Số đơn hàng thành công
        totalOrderShipping, // Số đơn hàng đang giao
        totalOrder, // Đơn hàng phát sinh
        totalMembers: allMembers.length,
        userLevelData,
        totalCommissionValue,
      };
    } catch (error) {
      console.log(':error: ', error);
    }
  },

  async findReferCodeOwner(query) {
    let { referCode } = query;

    if (!referCode) {
      referCode = REFER_CODE_ROOT;
    }

    console.log(referCode);
    const parent = await strapi.db
      .query('plugin::users-permissions.user')
      .findOne({
        where: {
          referCode: {
            $eqi: referCode,
          },
        },
      });

    if (!parent) {
      throw new ApplicationError('Invalid refer code');
    }

    return { data: parent };
  },

  async checkFirstCombo(orderInfo, user) {
    try {
      if (user.isBuyFirstCombo) {
        return false;
      }

      if (user.level != Level.Member) {
        return;
      }

      const firstComboName = await strapi
        .service('api::config.config')
        .getFirstComboName();

      if (!orderInfo || !orderInfo.orderData?.items) {
        return;
      }

      const orderData = orderInfo.orderData.items;

      return orderData.some((item) => {
        const productCats = item.product.product_cats;
        const productCatsData = item.product.product_cats?.data;

        return (
          (Array.isArray(productCats) &&
            productCats.some(
              (cat) => cat.name?.toLowerCase() === firstComboName.toLowerCase()
            )) ||
          (Array.isArray(productCatsData) &&
            productCatsData.some(
              (cat) =>
                cat?.attributes?.name?.toLowerCase() ===
                firstComboName.toLowerCase()
            ))
        );
      });
    } catch (error) {
      console.log(e);
      return;
    }
  },

  async checkIsCombo(orderInfo) {
    try {
      const firstComboName = await strapi
        .service('api::config.config')
        .getFirstComboName();

      if (!orderInfo || !orderInfo.orderData?.items) {
        return;
      }

      const orderData = orderInfo.orderData.items;

      return orderData.some((item) => {
        const productCats = item.product.product_cats;
        const productCatsData = item.product.product_cats?.data;

        return (
          (Array.isArray(productCats) &&
            productCats.some(
              (cat) => cat.name?.toLowerCase() === firstComboName.toLowerCase()
            )) ||
          (Array.isArray(productCatsData) &&
            productCatsData.some(
              (cat) =>
                cat?.attributes?.name?.toLowerCase() ===
                firstComboName.toLowerCase()
            ))
        );
      });
    } catch (error) {
      console.log(e);
      return;
    }
  },
});
