const dayjs = require('dayjs');
const {
  baseAfterCreate,
  baseAfterUpdate,
  baseAfterDelete,
} = require('../../../../utils/base/lifecycles');
const {
  OrderStatus,
  BonusPercent,
  TransactionType,
  Vat,
  BonusPercentForChild,
  Level,
  BonusPointPercent,
  NewUserRegisterCommissionValue,
  NewUserRegisterCommissionValueForPresident,
  CommissionValueForVicePresident,
  IndirectCommission,
  DirectCommission,
  LevelName,
  OrderType,
} = require('../../../../utils/constants');
const {
  addMoneyTo,
  sendNotiBaseOnLevel,
  trackEvent,
  calcUserLevelUpdated,
  trackError,
  calculatePersonSale,
  queryUserById,
  calculateCommissionForLevel,
  calculateCommissionForParentLevel,
  calculateMonthSale,
  calculateIndirectCommissionForParentLevel,
  orderOriginalPrice,
} = require('../../../../utils/utils');

const bonusForUser = async (userId, orderId, order) => {
  const user = await queryUserById(userId);
  if (user) {
    const level = user.level;
    if (level === BonusPercent.length) return;
    trackEvent('directlyBonus', { orderId, userId: user.id });
    if (!user.blocked) {
      const { balance = 0 } = user;
      const rateCommissionPartner =
        parseFloat(user.rateCommissionPartner) / 100;
      const amount = rateCommissionPartner
        ? Math.round(parseInt(order.finalPrice) * rateCommissionPartner)
        : calculateCommissionForLevel(order, level);
      const newBalance = Number(balance) + Number(amount);
      await addMoneyTo({
        to: user.id,
        amount,
        afterBalance: newBalance,
        type: TransactionType.CommissionOrder,
        orderId,
      });
      await strapi.db.query('plugin::users-permissions.user').update({
        where: { id: user.id },
        data: {
          commission: {
            ...user.commission,
            totalCommission: parseInt(user.commission.totalCommission) + amount,
            directlyCommission:
              parseInt(user.commission.directlyCommission) + amount,
          },
        },
      });
      trackEvent('directlyBonus', {
        orderId,
        userId: user.id,
        amount,
        message: 'Success',
      });
    } else {
      trackEvent('directlyBonus', {
        orderId,
        userId: user.id,
        message: 'blocked',
      });
    }
  }
};

const bonusForParent = async (userId, order, referCode, orderId) => {
  // if (level === BonusPercent.length) return;
  const me = await queryUserById(userId, { populate: { fParent: true } });
  if (!me.fParent) return;
  // if(!me.level === BonusPercent.length) return;
  trackEvent('bonusForParent', { orderId, parent: me.fParent });
  const myParent = await queryUserById(me.fParent);
  if (myParent && !myParent.blocked) {
    const { balance = 0 } = myParent;
    const childLevel = me.level;
    const parentLevel = myParent.level;
    if (childLevel < parentLevel) {
      const amount = calculateCommissionForParentLevel(
        order,
        parentLevel,
        childLevel
      );
      const newBalance = Number(balance) + Number(amount);

      await addMoneyTo({
        to: me.fParent,
        amount,
        afterBalance: newBalance,
        type: TransactionType.CommissionOrder,
        orderId,
      });
      await strapi.db.query('plugin::users-permissions.user').update({
        where: { id: me.fParent },
        data: {
          commission: {
            ...myParent.commission,
            totalCommission:
              parseInt(myParent.commission?.totalCommission) + amount,
            manageCommission:
              parseInt(myParent.commission?.manageCommission) + amount,
          },
        },
      });
      trackEvent('bonusForParent', {
        orderId,
        parent: me.fParent,
        amount,
        message: 'Success',
      });
    } else {
      trackEvent('bonusForParent', {
        orderId,
        parent: me.fParent,
        message: `same level`,
      });
    }
  } else {
    trackEvent('bonusForParent', {
      orderId,
      message: `blocked`,
      parent: myParent.fParent,
    });
  }
  await bonusForParent(me.fParent, order, referCode, orderId);
};

const bonus = async (user, order) => {
  if (user.level !== Level.NewUser) {
    await bonusForUser(user.id, order.id, order);
    await bonusForParent(user.id, order, user.referCode, order.id);
  }
};

const updateUserLevel = async (u) => {
  const user = await queryUserById(u.id);
  if (user) {
    const newLevel = await calcUserLevelUpdated(user);
    if (newLevel) {
      await strapi.db.query('plugin::users-permissions.user').update({
        where: { id: user.id },
        data: {
          level: newLevel,
          extra: { ...user.extra, showPopupNewLevel: true },
        },
      });
      await sendNotiBaseOnLevel(user, newLevel);
    }

    const parent = await queryUserById(user.fParent);
    if (parent) {
      await updateUserLevel(parent);
    }
  }

  // const newLevelParent = await calcUserLevelUpdated(parent);
  // if (newLevelParent) {
  //   await strapi.db.query('plugin::users-permissions.user').update({
  //     where: { id: parent.id },
  //     data: {
  //       level: newLevelParent,
  //       extra: { ...parent.extra, showPopupNewLevel: true },
  //     },
  //   });
  //   await sendNotiBaseOnLevel(parent, newLevelParent);
  // }
};

const indirectCommission = async (user, order) => {
  if (user.level !== Level.NewUser) {
    const me = order.creator;
    const myParent = await queryUserById(me.fParent);
    const myParentParent = await queryUserById(myParent.fParent);
    let childLevel = me.level;
    let parentLevel = myParent.level;
    let finalChildLevel = childLevel;
    let finalParentLevel = parentLevel;
    const childIsCEO = childLevel === Level.CEO;
    const childIsCustomer =
      childLevel === Level.Customer && myParent.level === Level.Collaborator;
    const childIsAgency =
      childLevel === Level.Collaborator && myParent.level === Level.Agency;
    if (childIsCEO) {
      finalChildLevel = `${Level.CEO}F1`;
      finalParentLevel = `${Level.CEO}F0`;
    }
    if (childIsCustomer || childIsAgency || childIsCEO) {
      await commissionForParent(
        me.id,
        myParent.id,
        finalChildLevel,
        finalParentLevel,
        order,
        order.id
      );
    }
    if (childIsCEO && myParentParent) {
      finalChildLevel = `${Level.CEO}F2`;
      finalParentLevel = `${Level.CEO}F0`;
      await commissionForParent(
        me.id,
        myParentParent.id,
        finalChildLevel,
        finalParentLevel,
        order,
        order.id
      );
    }
  }
};

const commissionForParent = async (
  childId,
  parentId,
  childLevel,
  parentLevel,
  order,
  orderId
) => {
  const me = await queryUserById(childId, { populate: { fParent: true } });
  if (!me.fParent) return;
  const myParent = await queryUserById(parentId);
  if (myParent && !myParent.blocked) {
    const { balance = 0 } = myParent;

    const amount = calculateIndirectCommissionForParentLevel(
      order.finalPrice,
      parentLevel,
      childLevel
    );

    const newBalance = Number(balance) + Number(amount);
    const parentTotalComission = myParent.commission?.totalCommission || 0;
    const parentManageCommission = myParent.commission?.manageCommission || 0;
    await addMoneyTo({
      to: myParent.id,
      amount,
      afterBalance: newBalance,
      type: TransactionType.CommissionOrder,
      orderId,
    });
    await strapi.db.query('plugin::users-permissions.user').update({
      where: { id: myParent.id },
      data: {
        commission: {
          ...myParent.commission,
          totalCommission: parseInt(parentTotalComission) + amount,
          manageCommission: parseInt(parentManageCommission) + amount,
        },
      },
    });
    console.log(
      ' parseInt(myParent.commission?.totalCommission) + amount is',
      parseInt(myParent.commission?.totalCommission),
      amount
    );
    trackEvent('commissionForParent', {
      orderId,
      parent: parentId,
      amount,
      message: 'Success',
    });
  } else {
    trackEvent('commissionForParent', {
      orderId,
      message: `blocked`,
      parent: myParent.fParent,
    });
  }
};

// exmaple

const bonusPointAfterOrder = async (userId, order) => {
  const user = await queryUserById(userId);
  const userLevel = user.level || 0;
  const orderPrice =
    userLevel === Level.Customer ? orderOriginalPrice(order) : order.finalPrice;
  const curBonusPoints = user.bonusPoint || 0;
  const cashbackValue = Number(orderPrice) * BonusPointPercent;
  const newBonusPoint = curBonusPoints + cashbackValue;
  await strapi.db.query('plugin::users-permissions.user').update({
    where: { id: userId },
    data: { bonusPoint: newBonusPoint },
  });
  trackEvent('bonusPointAfterOrder', {
    orderId: order.id,
    userId,
    currentBonusPoint: curBonusPoints,
    newBonus: cashbackValue,
    totalBonusPoint: newBonusPoint,
    message: 'Success',
  });
};

const newUpdateUserLevel = async (userId, orderId) => {
  try {
    const user = await strapi.db
      .query('plugin::users-permissions.user')
      .findOne({
        where: { id: userId },
      });
    const userLevel = user?.level;
    if (userLevel !== Level.Customer) {
      return;
    }
    await strapi.db.query('plugin::users-permissions.user').update({
      where: { id: userId },
      data: {
        level: Level.Collaborator,
      },
    });
    trackEvent('newUpdateUserLevel', {
      orderId,
      userId,
      message: 'Success',
      level: userLevel,
      newLevel: Level.Collaborator,
    });
  } catch (err) {
    trackEvent('newUpdateUserLevel', {
      orderId,
      userId,
      message: 'Error',
      errorMessage: err.message,
    });
  }
};

const orderFirstCombo = async (user, order) => {
  const parentInfo = await strapi.db
    .query('plugin::users-permissions.user')
    .findOne({ where: { id: user.fParent } });

  if (!parentInfo) {
    console.log('payCommissionForTree::Cannot find user parent', user.fParent);
    return;
  }

  const amount = DirectCommission[parentInfo.level] || 0;

  const newBalance = Number(parentInfo.balance) + Number(amount);
  await addMoneyTo({
    to: parentInfo.id,
    amount,
    afterBalance: newBalance,
    type: TransactionType.CommissionOrder,
    orderId: order.id,
  });

  if (amount > 0) {
    await calculateIndirectCommission(parentInfo, order);
  }
  await strapi.db.query('plugin::users-permissions.user').update({
    where: { id: user.id },
    data: { isBuyFirstCombo: true },
  });

  return true;
};

const calculateIndirectCommission = async (parent, order) => {
  let currentParent = parent;
  let currentLevel = parent.level;
  while (currentParent.fParent) {
    const grandParent = await strapi.db
      .query('plugin::users-permissions.user')
      .findOne({ where: { id: currentParent.fParent } });
    if (grandParent && grandParent.level > currentLevel) {
      const indirectPolicy = IndirectCommission[grandParent.level];
      const amount = indirectPolicy ? indirectPolicy[currentLevel] || 0 : 0;
      const newBalance = Number(grandParent.balance) + Number(amount);
      if (amount > 0) {
        await addMoneyTo({
          to: grandParent.id,
          amount,
          afterBalance: newBalance,
          type: TransactionType.CommissionOrder,
          orderId: order.id,
        });
      }
      currentLevel = grandParent.level;
    }
    currentParent = grandParent;
  }
};

const upLevel = async (userId, orderId) => {
  try {
    const user = await strapi.db
      .query('plugin::users-permissions.user')
      .findOne({
        where: { id: userId },
      });
    const userLevel = user?.level;
    if (userLevel !== Level.Member) {
      return;
    }
    await strapi.db.query('plugin::users-permissions.user').update({
      where: { id: userId },
      data: {
        level: Level.GoldMember,
      },
    });
    trackEvent('newUpdateUserLevel', {
      orderId,
      userId,
      message: 'Success',
      level: userLevel,
      newLevel: Level.GoldMember,
    });

    if (user.fParent) {
      const parentInfo = await strapi.db
        .query('plugin::users-permissions.user')
        .findOne({
          where: { id: user.fParent },
        });

      const childrenIds = parentInfo.sChildren
        ? parentInfo.sChildren.split(',').map((id) => id.trim())
        : [];

      const children = await strapi.db
        .query('plugin::users-permissions.user')
        .findMany({
          where: {
            id: { $in: childrenIds },
          },
        });

      const goldMemberCount = children.filter(
        (child) => child.level === Level.GoldMember
      ).length;

      if (goldMemberCount >= 10 && parentInfo.level === Level.GoldMember) {
        await strapi.db.query('plugin::users-permissions.user').update({
          where: { id: user.fParent },
          data: {
            level: Level.Collaborator,
          },
        });

        trackEvent('newUpdateParentLevel', {
          parentId: user.fParent,
          message: 'Success',
          previousLevel: parentInfo.level,
          newLevel: Level.Collaborator,
          goldMemberChildren: goldMemberCount,
        });
      }
    }
  } catch (err) {
    trackEvent('newUpdateUserLevel', {
      orderId,
      userId,
      message: 'Error',
      errorMessage: err.message,
    });
  }
};

module.exports = {
  afterCreate(event) {
    baseAfterCreate(event);
  },
  async afterUpdate(event) {
    try {
      const { params } = event;
      baseAfterUpdate(event);
      const order = await strapi.db.query('api::order.order').findOne({
        where: { id: event.result.id },
        populate: { creator: true },
      });
      if (params?.data?.orderStatus === OrderStatus.Confirmed) {
        if (order.type === OrderType.COURSE) {
          for (const item of order.orderData.items) {
            const course = await strapi.db.query('api::course.course').findOne({
              where: { id: item.product.id },
              populate: { users: true },
            });
            if (!course) {
              throw new Error('Course not found');
            }
            const existingUserIds = course.users.map((user) => user.id);
            if (existingUserIds.includes(user.id)) {
              return { message: 'User already enrolled in the course' };
            }
            await strapi.db.query('api::course.course').update({
              where: { id: item.product.id },
              data: { users: [...existingUserIds, user.id] },
            });
          }
        }
      }
      if (params?.data?.orderStatus === OrderStatus.Success) {
        const isFirstCombo = await strapi
          .service('api::my-team.my-team')
          .checkFirstCombo(order, order.creator);

        if (isFirstCombo) {
          await orderFirstCombo(order.creator, order);
          await upLevel(order.creator.id, order.id);
        }

        await calculateMonthSale(order.creator.id, order);
        await calculatePersonSale(order.creator.id);
        // await bonus(order.creator, order);
        // await indirectCommission(order.creator, order);
        // await updateUserLevel(order.creator);
        // await newUpdateUserLevel(order.creator.id, order.id);
        // await bonusPointAfterOrder(order.creator.id, order);
      }
    } catch (e) {
      console.log('===>', e);
      trackEvent('error afterUpdate', { e, orderId: event?.params?.data?.id });
    }
  },
  afterDelete(event) {
    baseAfterDelete(event);
  },
};
