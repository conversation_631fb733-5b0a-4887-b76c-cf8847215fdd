module.exports = {
  routes: [
    {
      method: 'GET',
      path: '/configs/system',
      handler: 'config.getSystemConfig',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/configs/status',
      handler: 'config.getSystemStatus',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/configs/app',
      handler: 'config.getAppConfig',
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
