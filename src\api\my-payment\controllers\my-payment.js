'use strict';

/**
 * A set of functions called "actions" for `my-payment`
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::payment.payment', ({ strapi }) => ({
  async find(ctx) {
    ctx.query = {
      ...ctx.query,
      filters: {
        ...ctx.query.filters,
        receiver: ctx.state.user.id,
      },
      sort: { createdAt: 'desc' },
    };
    const { data, meta } = await super.find(ctx);
    return { data, meta };
  },
}));
