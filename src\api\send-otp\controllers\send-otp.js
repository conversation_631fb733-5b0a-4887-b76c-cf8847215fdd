'use strict';

/**
 * A set of functions called "actions" for `send-otp`
 */

module.exports = {
  send: async (ctx, next) => {
    try {
      const data = await strapi
        .service('api::send-otp.send-otp')
        .send(ctx.request.body);
      ctx.body = data;
    } catch (err) {
      ctx.body = err;
    }
  },
  verify: async (ctx, next) => {
    try {
      const data = await strapi
        .service('api::send-otp.send-otp')
        .verify(ctx.request.body);
      ctx.body = data;
    } catch (err) {
      ctx.body = err;
    }
  },
  forgot: async (ctx, next) => {
    try {
      const data = await strapi
        .service('api::send-otp.send-otp')
        .forgot(ctx.request.body);
      ctx.body = data;
    } catch (err) {
      ctx.body = err;
    }
  },
  verifyForgot: async (ctx, next) => {
    try {
      const data = await strapi
        .service('api::send-otp.send-otp')
        .verifyForgot(ctx.request.body);
      ctx.body = data;
    } catch (err) {
      ctx.body = err;
    }
  },
  resetPassword: async (ctx, next) => {
    try {
      const data = await strapi
        .service('api::send-otp.send-otp')
        .resetPassword(ctx.request.body);
      ctx.body = data;
    } catch (err) {
      ctx.body = err;
    }
  },
};
