'use strict';

const dayjs = require('dayjs');
const {
  calcUserLevelUpdated,
  addMoneyTo,
  calculatePersonSale,
  inactiveUserAfterAMonth,
} = require('../../../utils/utils');
const {
  TransactionType,
  OrderStatus,
  BonusPercentPerMonth,
  PercentToMainWallet,
  BonusPercent,
  CashbackTriAn,
  DiscountSecondOrder,
} = require('../../../utils/constants');
const {
  calculateSum,
  calculateBonusPerMonth,
} = require('../../../../config/cron/calculateBonusPerMonth');
const { createBinaryTree } = require('../../../utils/TreeUtils');
const {
  expireInactiveUser,
} = require('../../../../config/cron/expireInactiveUser');
const {
  withdrawSubWallet,
} = require('../../../../config/cron/withdrawSubWallet');
const {
  markSubWalletPaymentDone,
} = require('../../../../config/cron/markSubWalletPaymentDone');

/**
 * A set of functions called "actions" for `test-api`
 */

async function changeAllUserToUnverified() {
  const res = await strapi.db
    .query('plugin::users-permissions.user')
    .updateMany({
      where: {
        verified: true,
        referCode: {
          $ne: 'KP000001',
        },
        referCode: {
          $ne: 'KP000002',
        },
        referCode: {
          $ne: 'KP000003',
        },
        // referCode: {
        //   $eq: 'KP679132',
        // },
      },
      data: {
        verified: false,
      },
    });
  console.log('🚀 ~ file: test-api.js:29 ~ res ~ res:', res);
  return 'ok';
}

const addChildren = async (data) => {
  const u = await strapi.db.query('plugin::users-permissions.user').findOne({
    where: {
      phone: data.phone,
    },
    populate: {
      sChildren: true,
    },
  });
  const childrenIds = [];
  for (const phone of data.children) {
    const ccu = await strapi.db
      .query('plugin::users-permissions.user')
      .findOne({
        where: {
          phone: phone,
        },
      });
    ccu.id && childrenIds.push(ccu.id);
  }
  const oldChildren = u.sChildren?.split(',') || [];
  const res = await strapi.db.query('plugin::users-permissions.user').update({
    where: {
      phone: data.phone,
    },
    data: {
      sChildren: [...oldChildren, ...childrenIds],
    },
  });
  for (const id of childrenIds) {
    await strapi.db.query('plugin::users-permissions.user').update({
      where: {
        id: id,
      },
      data: {
        fParent: u.id,
      },
    });
  }
  return res;
};

const copyChildren = async () => {
  const all = await strapi.db.query('plugin::users-permissions.user').findMany({
    populate: {
      fChildren: true,
    },
  });
  console.log('🚀 ~ file: test-api.js:81 ~ copyChildren ~ all:', all.length);
  for (const u of all) {
    if (u.fChildren.length) {
      await strapi.db.query('plugin::users-permissions.user').update({
        where: {
          id: u.id,
        },
        data: {
          sChildren: u.fChildren.map((o) => o.id).join(','),
        },
      });
    }
  }
  return 'ok';
};

const updateLevel = async () => {
  const all = await strapi.db.query('plugin::users-permissions.user').findMany({
    populate: { sChildren: true },
  });
  console.log('🚀 ~ file: test-api.js:107 ~ all ~ all:', all.length);
  const a = [];
  for (const u of all) {
    const level = await calcUserLevelUpdated(u);
    if (level) {
      await strapi.db.query('plugin::users-permissions.user').update({
        where: { id: u.id },
        data: { level },
      });
      a.push(`${u.id}: ${u.level},${level}`);
    }
  }
  return a;
};

const validateChildrenAndParent = async () => {
  const res = await strapi.db
    .query('plugin::users-permissions.user')
    .findMany();
  const objUser = Object.fromEntries(res.map((o) => [o.id, o]));
  for (const u of res) {
    if (u.sChildren) {
      const children = u.sChildren.split(',');
      for (const c of children) {
        const child = objUser[c];
        if (!child) {
          console.log('xxxxx id not found', c, u.id);
        } else {
          if (u.id !== child.fParent && !child.blocked) {
            console.log(
              `child not match: child of ${u.id} is ${child.id}, parent of ${child.id}: ${child.fParent}`
            );
          }
        }
      }
    }
  }

  console.log('==============');
  for (const u of res) {
    if (u.fParent) {
      const parent = objUser[u.fParent];
      if (!parent) {
        console.log('xxxxx id not found', u.fParent, u.id);
      } else {
        const children = parent.sChildren ? parent.sChildren.split(',') : [];
        if (!children.includes(String(u.id)) && !u.blocked) {
          console.log(
            'xxxxx parent not match: user',
            u.id,
            'his parent:',
            u.fParent,
            'child of u.fParent:',
            parent.sChildren
          );
        }
      }
    }
  }
  return 'ok';
};

const checkSubAccount = async () => {
  const allUser = await strapi.db
    .query('plugin::users-permissions.user')
    .findMany();
  const saved = {};
  for (const u of allUser) {
    if (u.phone.includes('-')) {
      const phone = u.phone.split('-')[0];
      const user = await strapi.db
        .query('plugin::users-permissions.user')
        .findOne({
          where: { phone },
        });
      if (!saved[phone]) {
        console.log('===========================', u.phone);
        saved[phone] = true;
        const numberOfSubAccount = user.numberOfSubAccount;
        if (numberOfSubAccount) {
          let enough = true;
          Array(numberOfSubAccount)
            .fill(1)
            .forEach((o, i) => {
              const phoneToFind = phone + '-' + (i + 1);
              console.log('----phoneToFind:', phoneToFind);
              const index = allUser.findIndex((oo) => oo.phone === phoneToFind);
              if (index === -1) {
                enough = false;
              }
            });
          console.log('ENOUGH ', phone, enough);
        } else {
          console.log('empty number of sub account', phone);
        }
      }
    }
  }
  return 'ok';
};

const minusBalance = async () => {
  // const res = await strapi.db
  //   .query('plugin::users-permissions.user')
  //   .findMany();
  // const start = dayjs()
  //   .startOf('month')
  //   .subtract(3, 'day')
  //   // .add(7, 'h')
  //   .add(13, 'h')
  //   .toDate();
  // console.log('🚀 ~ file: test-api.js:161 ~ minusBalance ~ start:', start);
  // const end = dayjs()
  //   .startOf('month')
  //   // .add(7, 'h')
  //   .toDate();
  // console.log('🚀 ~ file: test-api.js:163 ~ minusBalance ~ end:', end);
  // for (const u of res) {
  //   if (u.balance && u.phone === '**********') {
  //     const transaction = await strapi.db
  //       .query('api::transaction.transaction')
  //       .findMany({
  //         where: {
  //           to: u.id,
  //           createdAt: {
  //             $gte: start,
  //             $lte: end,
  //           },
  //         },
  //       });
  //     console.log(
  //       '🚀 ~ file: test-api.js:182 ~ minusBalance ~ transaction:',
  //       transaction
  //     );
  //     const total = transaction.reduce((a, b) => a + b.amount, 0);
  //     console.log('🚀 ~ file: test-api.js:187 ~ minusBalance ~ total:', total);
  //     console.log(
  //       '🚀 ~ file: test-api.js:190 ~ minusBalance ~ u.balance:',
  //       u.balance
  //     );
  //     if (total) {
  //       console.log(u.phone + ': ' + (u.balance - total));
  //     }
  //   }
  // }
  const allUser = await strapi.db
    .query('plugin::users-permissions.user')
    .findMany({
      filters: {
        blocked: false,
      },
    });
  console.log(
    '🚀 ~ file: payForUserPerWeek.js:8 ~ payForUserPerWeek ~ allUser:',
    allUser.length
  );
  const firstDayLastWeek = dayjs()
    .startOf('month')
    .subtract(3, 'day')
    .add(13, 'h');
  const lastDayLastWeek = dayjs().startOf('month');
  const lastWeekString = `${firstDayLastWeek.format(
    'DD/MM/YYYY'
  )}-${lastDayLastWeek.format('DD/MM/YYYY')}`;
  // payment for user
  for (const user of allUser) {
    if (user.balance > 0) {
      // get amount
      const transaction = await strapi.db
        .query('api::transaction.transaction')
        .findMany({
          where: {
            to: user.id,
            createdAt: {
              $gte: firstDayLastWeek.toDate(),
              $lte: lastDayLastWeek.toDate(),
            },
          },
        });
      const amount = transaction.reduce((a, b) => a + b.amount, 0);
      /////
      const res = await strapi.db.query('api::payment.payment').findOne({
        where: {
          receiver: user.id,
          week: lastWeekString,
        },
      });
      if (amount) {
        console.log(
          '🚀 ~ file: test-api.js:242 ~ res ~ res:',
          user.phone,
          amount,
          user.balance - amount
        );
      }
      if (!res && amount) {
        //create payment
        const res = await strapi.db.query('api::payment.payment').create({
          data: {
            receiver: user.id,
            amount,
            week: lastWeekString,
            done: false,
            from: firstDayLastWeek.toDate(),
            to: lastDayLastWeek.toDate(),
          },
        });
        if (res.id) {
          console.log(
            '🚀 ~ file: test-api.js:263 ~ minusBalance ~ res.id:',
            res.id
          );
          await addMoneyTo({
            to: 0,
            amount,
            afterBalance: user.balance - amount,
            type: TransactionType.Withdraw,
            from: user.id,
          });
        }
      }
    }
  }
  return 'ok';
};

const checkUserLevel = async () => {
  const allUser = await strapi.db
    .query('plugin::users-permissions.user')
    .findMany();
  for (const u of allUser) {
    const level = await calcUserLevelUpdated(u);
    if (level !== null) {
      console.log(
        '🚀 ~ file: test-api.js:318 ~ checkUserLevel ~ level:',
        u.phone,
        level,
        u.level
      );
      await strapi.db.query('plugin::users-permissions.user').update({
        where: { id: u.id },
        data: { level },
      });
    }
  }
  return 'ok';
};

const resetKyc = async () => {
  const allUser = await strapi.db
    .query('plugin::users-permissions.user')
    .findMany();
  for (const u of allUser) {
    if (u.verified) {
      await strapi.db.query('plugin::users-permissions.user').update({
        where: { id: u.id },
        data: { verified: false },
      });
    }
  }
  return 'ok';
};

const markDonePayment = async () => {
  const payments = await strapi.db.query('api::payment.payment').findMany({
    where: {
      done: false,
      week: '05/11/2023-11/11/2023',
    },
  });
  console.log(
    '🚀 ~ file: test-api.js:356 ~ payments ~ payments:',
    payments.length
  );
  const a = await strapi.db.query('api::payment.payment').updateMany({
    where: {
      id: { $in: payments.map((o) => o.id) },
    },
    data: {
      done: true,
    },
  });
  console.log('🚀 ~ file: test-api.js:360 ~ markDonePayment ~ a:', a);
  return 'ok';
};

const calculateMonthSale = async (userId, shouldUpdate) => {
  const saleMonth = await strapi.db.query('api::sale.sale').findOne({
    where: {
      userId,
      month: new Date().getMonth() + 1,
      year: new Date().getFullYear(),
    },
  });
  const orderThisMonth = await strapi.db.query('api::order.order').findMany({
    where: {
      creatorId: userId,
      createdAt: {
        $gte: dayjs().startOf('month').toDate(),
        $lte: dayjs().endOf('month').toDate(),
      },
      $or: [
        { orderStatus: OrderStatus.Confirmed },
        { orderStatus: OrderStatus.Shipping },
        { orderStatus: OrderStatus.Success },
      ],
    },
  });
  const monthSale = orderThisMonth.reduce(
    (total, order) => total + Number(order.finalPrice),
    0
  );
  if (!saleMonth) {
    if (monthSale > 0) {
      console.log('create sale month ', userId, monthSale);
      shouldUpdate &&
        (await strapi.db.query('api::sale.sale').create({
          data: {
            userId,
            month: new Date().getMonth() + 1,
            year: new Date().getFullYear(),
            sale: monthSale,
          },
        }));
    }
  } else {
    if (monthSale !== saleMonth.sale) {
      console.log('update sale month ', userId, monthSale);
      shouldUpdate &&
        (await strapi.db.query('api::sale.sale').update({
          where: {
            id: saleMonth.id,
          },
          data: {
            userId,
            month: new Date().getMonth() + 1,
            year: new Date().getFullYear(),
            sale: monthSale,
          },
        }));
    }
  }
};

const updateSaleTable = async (data) => {
  const { shouldUpdate } = data;
  console.log('🚀 ~ updateSaleTable ~ shouldUpdate:', shouldUpdate);
  const allUser = await strapi.db
    .query('plugin::users-permissions.user')
    .findMany({
      where: {
        blocked: false,
      },
    });
  const p = allUser.map((u) => calculateMonthSale(u.id, shouldUpdate));
  const a = await Promise.all(p);
  return a.length;
};

const checkNullQuantity = async () => {
  const allOrder = await strapi.db.query('api::order.order').findMany();
  console.log(
    '🚀 ~ file: test-api.js:401 ~ checkNullQuantity ~ all:',
    allOrder.length
  );
  for (const order of allOrder) {
    for (const item of order.orderData.items) {
      if (item.quantity === null)
        console.log(
          '🚀 ~ file: test-api.js:433 ~ checkNullQuantity ~ item:',
          order.id
        );
    }
    // await strapi.db.query('api::product.product').update({
    //   where: { id: p.id },
    //   data: { quantity: 0 },

    // });
  }
  return 'ok';
};

async function printSumOfChildren(rootNode, start, end) {
  if (!rootNode) return;

  // console.log(
  //   `Node ${
  //     rootNode.name
  //   }: Sum of Children (Max ${maxLevel} Levels): ${calculateSum(
  //     rootNode,
  //     maxLevel
  //   )}`
  // );
  const sumOfChildren = calculateSum(
    rootNode,
    0,
    rootNode.attributes.id === 636
  );
  // console.log(
  //   '🚀 ~ file: test-api.js:478 ~ printSumOfChildren ~ sumOfChildren:',
  //   sumOfChildren
  // );
  if (sumOfChildren) {
    const u = await strapi.db.query('plugin::users-permissions.user').findOne({
      where: { id: rootNode.attributes.id, blocked: false },
    });
    // những user mới tạo đơn đầu trong tháng này thì sẽ bỏ qua
    const firstOrderCreatedAt = dayjs(u.firstOrderCreatedAt);
    if (u && firstOrderCreatedAt.isBefore(start)) {
      const amount = sumOfChildren * BonusPercentPerMonth;
      const amountToMain = parseInt(amount * PercentToMainWallet);
      const amountToSub = parseInt(amount * (1 - PercentToMainWallet));
      // console.log(
      //   '🚀 ~ file: test-api.js:465 ~ printSumOfChildren ~ amountToMain:',
      //   rootNode.attributes.id,
      //   amount,
      //   amountToMain,
      //   amountToSub
      // );
      // return;
      await addMoneyTo({
        to: rootNode.attributes.id,
        afterBalance: u.balance + amountToMain,
        amount: amountToMain,
        type: TransactionType.CommissionSystem,
      });
      await addMoneyTo({
        to: rootNode.attributes.id,
        afterBalance: u.subBalance + amountToSub,
        amount: amountToSub,
        type: TransactionType.CommissionSystem,
        targetWallet: 'sub',
      });
    }
  }

  if (rootNode.children) {
    for (const child of rootNode.children) {
      await printSumOfChildren(child, start, end);
    }
  }
}

const calcBonusPerMonth = async () => {
  const start = dayjs().subtract(1, 'month').startOf('month');
  const end = dayjs().startOf('month').add(3, 'day');
  const allUser = await strapi.db
    .query('plugin::users-permissions.user')
    .findMany({
      where: { blocked: false },
    });
  console.log(
    '🚀 ~ file: calculateBonusPerMonth.js:9 ~ calculateBonusPerMonth ~ allUser:',
    allUser.length
  );

  // bonus for last week
  const users = await strapi.entityService.findMany(
    'plugin::users-permissions.user',
    {
      filters: {
        blocked: false,
        status: 'active',
      },
      sort: { createdAt: 'asc' },
      // sort: { firstOrderCreatedAt: 'asc' },
    }
  );
  const superUsers = await strapi.entityService.findMany(
    'plugin::users-permissions.user',
    {
      filters: {
        $or: [{ id: 1 }, { id: 3 }, { id: 4 }],
      },
      sort: { createdAt: 'asc' },
    }
  );

  const allOrder = await strapi.db.query('api::order.order').findMany({
    where: {
      firstOrder: { $null: true },
      createdAt: {
        $lte: end.toDate(),
        $gte: start.toDate(),
      },
      $or: [
        { orderStatus: OrderStatus.Success },
        { orderStatus: OrderStatus.Shipping },
        { orderStatus: OrderStatus.Confirmed },
      ],
    },
  });
  users.unshift(...superUsers);
  const u = users.map((o, i) => ({
    name: o.phone,
    attributes: {
      id: o.id,
      referCode: o.referCode,
      name: o.name,
      sale: allOrder
        .filter((o1) => o1.creatorId == o.id)
        .reduce((acc, curr) => acc + curr.finalPrice, 0),
      teamSale: o.myTeamSale,
      level: o.level,
      index: i,
    },
    createdAt: o.createdAt,
    blocked: o.blocked,
  }));
  const tree = createBinaryTree(u);
  await strapi.db.query('api::binary-tree.binary-tree').create({
    data: {
      json: tree,
    },
  });
  await printSumOfChildren(tree, start, end);
  return 'ok';
};

const removeCommissionSystem = async () => {
  const allTnx = await strapi.db
    .query('api::transaction.transaction')
    .findMany({
      where: {
        type: TransactionType.CommissionSystem,
        createdAt: {
          $lte: dayjs('17:48 14/12/2023', 'HH:mm DD/MM/YYYY').toDate(),
          $gte: dayjs('17:40 14/12/2023', 'HH:mm DD/MM/YYYY').toDate(),
          // $gte: dayjs().startOf('month').toDate(),
          // $lte: dayjs().toDate(),
        },
      },
    });
  // console.log(
  //   '🚀 ~ file: test-api.js:609 ~ removeCommissionSystem ~ a:',
  //   allTnx.length
  // );

  // return;
  for (const tnx of allTnx) {
    if (tnx.targetWallet === 'main') {
      const u = await strapi.db
        .query('plugin::users-permissions.user')
        .findOne({ where: { id: tnx.to } });
      const newBalance = Number(u.balance) - Number(tnx.amount);
      if (newBalance >= 0) {
        await strapi.db.query('plugin::users-permissions.user').update({
          where: { id: tnx.to },
          data: { balance: newBalance, subBalance: 0 },
        });
        await strapi.entityService.delete(
          'api::transaction.transaction',
          tnx.id
        );
      } else {
        console.log('error 1, ', tnx.to, newBalance, tnx.amount);
      }
    } else if (tnx.targetWallet === 'sub') {
      await strapi.entityService.delete('api::transaction.transaction', tnx.id);
    }
  }
  return 'ok';
};

const removeWrongNoti = async () => {
  const res = await strapi.db.query('api::notification.notification').findMany({
    where: {
      content: {
        $contains: 'hoa hồng vào ví ',
      },
    },
  });
  for (const r of res) {
    await strapi.db.query('api::notification.notification').deleteMany({
      where: {
        id: r.id,
      },
    });
  }
  return 'ok';
};

const markFirstOrder = async () => {
  const allUser = await strapi.db
    .query('plugin::users-permissions.user')
    .findMany({
      where: {
        blocked: false,
      },
    });
  let i = 0;
  for (const user of allUser) {
    const first = await strapi.db.query('api::order.order').findOne({
      where: {
        creatorId: user.id,
        $or: [
          { orderStatus: OrderStatus.Success },
          { orderStatus: OrderStatus.Shipping },
          { orderStatus: OrderStatus.Confirmed },
        ],
      },
      limit: 1,
      orderBy: {
        createdAt: 'asc',
      },
    });
    if (first && !first.firstOrder) {
      await strapi.db.query('api::order.order').update({
        where: { id: first.id },
        data: { firstOrder: true },
      });
    }
    i++;
  }
  console.log(' done xxxxxxxxxxxxxxxxxx', i);
};

const removeUserInTime = async () => {
  // const allUser = await strapi.db
  //   .query('plugin::users-permissions.user')
  //   .findMany({
  //     where: {
  //       createdAt: {
  //         $lte: dayjs()
  //           .startOf('day')
  //           .subtract(1, 'day')
  //           .add(30, 'minute')
  //           .toDate(),
  //         $gte: dayjs().startOf('day').subtract(1, 'day').toDate(),
  //       },
  //     },
  //   });
  // for (const user of allUser) {
  //   await strapi.entityService.delete(
  //     'plugin::users-permissions.user',
  //     user.id
  //   );
  // }
  const allUser = await strapi.db
    .query('plugin::users-permissions.user')
    .findMany({
      where: {
        blocked: false,
      },
    });
  for (const user of allUser) {
    const children = user.sChildren ? user.sChildren.split(',') : [];
    if (children.length > 0) {
      const newChildren = children.filter((c) => {
        const childInt = parseInt(c);
        return childInt < 2193 || childInt > 2320;
      });
      if (newChildren.length !== children.length) {
        await strapi.db.query('plugin::users-permissions.user').update({
          where: { id: user.id },
          data: { sChildren: newChildren.join(',') },
        });
      }
    }
  }
  return 'ok';
};

const addFieldFirstOrderCreatedAt = async () => {
  const all = await strapi.db.query('plugin::users-permissions.user').findMany({
    where: { blocked: false, firstOrderCreatedAt: { $null: true } },
  });
  for (const user of all) {
    if (!user.firstOrderCreatedAt) {
      const firstOrder = await strapi.db.query('api::order.order').findOne({
        where: {
          creatorId: user.id,
          $or: [
            { orderStatus: OrderStatus.Success },
            { orderStatus: OrderStatus.Shipping },
            { orderStatus: OrderStatus.Confirmed },
          ],
        },
        limit: 1,
        orderBy: { createdAt: 'asc' },
      });
      if (firstOrder) {
        await strapi.db.query('plugin::users-permissions.user').update({
          where: { id: user.id },
          data: { firstOrderCreatedAt: firstOrder.createdAt },
        });
      }
    }
  }
  return 'ok';
};

const month12user = [
  'KP257751',
  'KP428426',
  'KP976633',
  'KP747106',
  'KP538155',
  'KP455033',
  'KP471296',
  'KP732891',
  'KP722725',
  'KP478829',
  'KP131669',
  'KP260684',
  'KP762821',
  'KP089905',
  'KP247209',
  'KP591545',
  'KP126419',
  'KP716975',
  'KP839863',
  'KP950906',
  'KP336944',
  'KP238519',
  'KP961621',
  'KP885655',
  'KP163906',
  'KP342263',
  'KP703908',
  'KP549294',
  'KP196256',
  'KP594142',
  'KP267835',
  'KP081677',
  'KP783843',
  'KP200073',
  'KP486389',
  'KP908049',
  'KP406870',
  'KP818874',
  'KP003936',
  'KP306814',
  'KP916909',
  'KP841395',
  'KP469036',
  'KP805237',
  'KP979733',
  'KP710784',
  'KP723550',
  'KP009240',
  'KP125202',
  'KP125248',
  'KP074456',
  'KP962844',
  'KP282660',
  'KP468606',
  'KP247676',
  'KP442008',
];

const clearSystemWallet = async () => {
  const allUser = await strapi.db
    .query('plugin::users-permissions.user')
    .findMany({ where: { blocked: false } });
  for (const user of allUser) {
    if (user.subBalance) {
      await strapi.db.query('plugin::users-permissions.user').update({
        where: { id: user.id },
        data: { subBalance: 0 },
      });
    }
  }
};

const copyUsers = [
  'KP000002',
  'KP000003',
  'KP938266',
  'KP368657',
  'KP553445',
  'KP797018',
  'KP620885',
  'KP219879',
  'KP751072',
  'KP776165',
  'KP624740',
  'KP959631',
  'KP347089',
  'KP821605',
  'KP086458',
  'KP787887',
  'KP509630',
  'KP041541',
  'KP487875',
  'KP355742',
];
const addUser = [
  'KP123394',
  'KP398858',
  'KP092122',
  'KP619156',
  'KP650373',
  'KP892580',
  'KP751076',
  'KP241775',
  'KP010914',
  'KP229159',
  'KP645931',
  'KP154175',
];
const moveTo300 = [
  'KP147217',
  'KP503137',
  'KP572718',
  'KP748921',
  'KP538961',
  'KP614272',
  'KP513159',
  'KP458094',
  'KP458809',
  'KP134961',
  'KP913348',
  'KP588291',
  'KP260537',
  'KP213718',
  'KP541014',
  'KP560642',
  'KP849004',
  'KP270155',
  'KP574610',
  'KP058675',
  'KP539214',
  'KP987666',
];
const moveToPos = [
  {
    id: 'KP228178',
    pos: 'KP598055',
  },
  {
    id: 'KP175649',
    pos: 'KP314971',
  },
  {
    id: 'KP910418',
    pos: 'KP120396',
  },
  {
    id: 'KP885546',
    pos: 'KP910418',
  },
  {
    id: 'KP982881',
    pos: 'KP885035',
  },
  {
    id: 'KP541014',
    pos: 'KP272614',
  },
  {
    id: 'KP382089',
    pos: 'KP328318',
  },
  {
    id: 'KP160913',
    pos: 'KP885546',
  },
];

const rearrangeUser = async () => {
  const firstUser = await strapi.db
    .query('plugin::users-permissions.user')
    .findOne({
      where: { referCode: 'KP000001' },
    });
  await strapi.db.query('plugin::users-permissions.user').update({
    where: { referCode: 'KP000001' },
    data: { firstOrderAt: firstUser.createdAt },
  });
  let time = firstUser.createdAt;
  for (const o of copyUsers) {
    const newDate = dayjs(time).add(1, 'min').toDate();
    await strapi.db.query('plugin::users-permissions.user').update({
      where: { referCode: o },
      data: { firstOrderAt: newDate },
    });
    time = newDate;
  }
  for (const o of addUser) {
    const newDate = dayjs(time).add(1, 'min').toDate();
    await strapi.db.query('plugin::users-permissions.user').update({
      where: { referCode: o },
      data: { firstOrderAt: newDate },
    });
    time = newDate;
  }
  const id300 = await strapi.db
    .query('plugin::users-permissions.user')
    .findOne({
      where: { referCode: 'KP148949' },
    });
  time = dayjs(id300.firstOrderAt).add(1, 'min').toDate();
  for (const o of moveTo300) {
    const newDate = dayjs(time).add(1, 'min').toDate();
    await strapi.db.query('plugin::users-permissions.user').update({
      where: { referCode: o },
      data: { firstOrderAt: newDate },
    });
    time = newDate;
  }
  ////////////////
  for (const move of moveToPos) {
    const newPos = await strapi.db
      .query('plugin::users-permissions.user')
      .findOne({
        where: { referCode: move.pos },
      });
    if (newPos.firstOrderAt) {
      await strapi.db.query('plugin::users-permissions.user').update({
        where: { referCode: move.id },
        data: {
          firstOrderAt: dayjs(newPos.firstOrderAt).add(1, 'min').toDate(),
        },
      });
    } else {
      console.log('not found newPos.firstOrderCreatedAt ', move.pos);
    }
  }
  return 'ok';
};

const copyFirstOrderCreatedAt = async () => {
  // const start12 = dayjs('00:00 11/12/2023', 'HH:mm DD/MM/YYYY');
  const allUser = await strapi.db
    .query('plugin::users-permissions.user')
    .findMany({
      where: {
        blocked: false,
        firstOrderCreatedAt: { $notNull: true },
        firstOrderAt: { $null: true },
        // createdAt: { $gt: start12.toDate() },
      },
    });
  for (const u of allUser) {
    const latestOrder = await strapi.db.query('api::order.order').findMany({
      where: {
        creatorId: u.id,
        $or: [
          { orderStatus: OrderStatus.Success },
          { orderStatus: OrderStatus.Shipping },
          { orderStatus: OrderStatus.Confirmed },
        ],
      },
      orderBy: { createdAt: 'asc' },
      limit: 1,
    });
    if (latestOrder.length) {
      await strapi.db.query('plugin::users-permissions.user').update({
        where: { id: u.id },
        data: { firstOrderAt: latestOrder[0].createdAt },
      });
      console.log('fill firstOrderAt ', u.id);
    }
  }
  return 'ok';
};

const printOrder11 = async () => {
  const allUser = await strapi.db
    .query('plugin::users-permissions.user')
    .findMany({
      where: {
        blocked: false,
        status: 'active',
        firstOrderAt: { $notNull: true },
      },
      orderBy: {
        firstOrderAt: 'asc',
      },
    });
  console.log(
    'xxx',
    allUser.findIndex((o) => o.referCode === 'KP934761')
  );
};

const activeUser = async () => {
  const start1 = dayjs('00:00 01/01/2024', 'HH:mm DD/MM/YYYY');
  const users = await strapi.db
    .query('plugin::users-permissions.user')
    .findMany({
      where: { blocked: false, status: 'inactive' },
    });
  for (const u of users) {
    const orders = await strapi.db.query('api::order.order').findMany({
      where: {
        creatorId: u.id,
        $or: [
          { orderStatus: OrderStatus.Success },
          { orderStatus: OrderStatus.Shipping },
          { orderStatus: OrderStatus.Confirmed },
        ],
        createdAt: { $gte: start1.toDate() },
      },
      limit: 1,
      orderBy: {
        createdAt: 'asc',
      },
    });
    if (orders.length) {
      console.log('🚀 ~ //awaitstrapi.db.query ~ u.id:', u.id);
      await strapi.db.query('plugin::users-permissions.user').update({
        where: { id: u.id },
        data: { status: 'active' },
      });
    }
  }
};

const calculateCommission = (user, allUser, allOrder, level, debug) => {
  // console.log('🚀 ~ calculateCommission ~ user:', user.id, level);
  if (level === BonusPercent.length) return 0;
  const firstOrder = allOrder.find(
    (o) => o.creatorId == user.id && o.firstOrder
  );
  // if (user.referCode === 'KP739711') console.log('xxxx', firstOrder);
  const finalPrice = firstOrder ? firstOrder.finalPrice : 0;
  let amount = Math.round((finalPrice * BonusPercent[level]) / 100);
  if (debug)
    console.log(
      '🚀 ~ calculateCommission ~ amount:',
      user.id,
      user.referCode,
      level,
      firstOrder?.id || 0,
      amount
    );
  for (const child of user.sChildren ? user.sChildren.split(',') : []) {
    const childUser = allUser.find((u) => u.id == child);
    if (childUser) {
      amount += calculateCommission(
        childUser,
        allUser,
        allOrder,
        level + 1,
        debug
      );
    }
  }
  // if (debug) console.log('🚀 ~ calculateCommission ~ amount:', amount);
  return amount;
};

const calculateCommissionUser = (user, allUser, allOrder, debug) => {
  debug &&
    console.log('🚀 ~ start debuggggggg ~ debug:', user.id, user.referCode);
  let amount = 0;
  for (const child of user.sChildren ? user.sChildren.split(',') : []) {
    const childUser = allUser.find((u) => u.id == child);
    if (childUser) {
      amount += calculateCommission(childUser, allUser, allOrder, 0, debug);
    }
  }
  return amount;
};

const verifyCommission = async () => {
  const start = dayjs('00:00 14/01/2024', 'HH:mm DD/MM/YYYY');
  const end = dayjs('00:00 20/01/2024', 'HH:mm DD/MM/YYYY').endOf('day');
  const allUsers = await strapi.db
    .query('plugin::users-permissions.user')
    .findMany({
      where: { blocked: false },
    });
  // console.log(
  //   '🚀 ~ verifyCommission ~ allUsers:',
  //   allUsers.find((o) => o.referCode === 'KP739711')
  // );
  const allOrder = await strapi.db.query('api::order.order').findMany({
    where: {
      $or: [
        { orderStatus: OrderStatus.Success },
        { orderStatus: OrderStatus.Shipping },
        { orderStatus: OrderStatus.Confirmed },
      ],
      createdAt: { $gte: start.toDate(), $lte: end.toDate() },
    },
  });
  for (const user of allUsers) {
    const commission = calculateCommissionUser(
      user,
      allUsers,
      allOrder,
      user.referCode === 'KP731313'
    );
    // if (user.id === 2726) {
    //   console.log('🚀 ~ verifyCommission ~ commission:', user.id, commission);
    //   break;
    // }
    if (commission > 0) {
      console.log('xxxx', user.id, user.referCode, commission);
    }
  }
};

const latePayment = async () => {
  const start = dayjs('00:00 21/01/2024', 'HH:mm DD/MM/YYYY');
  const end = dayjs('00:00 27/01/2024', 'HH:mm DD/MM/YYYY').endOf('day');
  const allTnx = await strapi.db
    .query('api::transaction.transaction')
    .findMany({
      where: {
        targetWallet: 'main',
        createdAt: {
          $gt: start.toDate(),
          $lt: end.toDate(),
        },
        type: TransactionType.CommissionOrder,
      },
    });
  for (const tnx of allTnx) {
    if (tnx.orderId) {
      const order = await strapi.db.query('api::order.order').findOne({
        where: { id: tnx.orderId },
      });
      if (order && dayjs(order.createdAt).isBefore(start)) {
        console.log(
          'order id ',
          tnx.orderId,
          dayjs(tnx.createdAt),
          dayjs(order.createdAt)
        );
      }
    }
  }
};

const fillStatus = async () => {
  const start = dayjs('00:00 11/12/2023', 'HH:mm DD/MM/YYYY');
  const start12 = dayjs('00:00 04/12/2023', 'HH:mm DD/MM/YYYY');
  const allUser = await strapi.db
    .query('plugin::users-permissions.user')
    .findMany({
      where: {
        blocked: false,
        firstOrderCreatedAt: { $notNull: true },
        createdAt: { $gt: start.toDate() },
      },
    });
  console.log('🚀 ~ fillStatus ~ allUser:', allUser.length);
  for (const u of allUser) {
    const latestOrder = await strapi.db.query('api::order.order').count({
      where: {
        creatorId: u.id,
        $or: [
          { orderStatus: OrderStatus.Success },
          { orderStatus: OrderStatus.Shipping },
          { orderStatus: OrderStatus.Confirmed },
        ],
        createdAt: { $gt: start12.toDate() },
      },
      orderBy: { createdAt: 'desc' },
      limit: 1,
    });
    // if(u.id === 3050) console.log(latestOrder)
    if (latestOrder) {
      console.log('xxxxx', u.id, 'active');
      await strapi.db.query('plugin::users-permissions.user').update({
        where: { id: u.id },
        data: { status: 'active' },
      });
    } else {
      await strapi.db.query('plugin::users-permissions.user').update({
        where: { id: u.id },
        data: { status: 'inactive' },
      });
    }
  }
  return 'ok';
};

const removeDup = (arr) => {
  return arr.filter((val, index) => arr.indexOf(val) === index);
};

const calcTriAnSecondOrder = async () => {
  const start = dayjs('00:00 01/02/2024', 'HH:mm DD/MM/YYYY');
  const end = dayjs();
  const allOrder = await strapi.db.query('api::order.order').findMany({
    where: {
      $or: [
        { orderStatus: OrderStatus.Success },
        { orderStatus: OrderStatus.Shipping },
        { orderStatus: OrderStatus.Confirmed },
      ],
      createdAt: { $gte: start.toDate(), $lte: end.toDate() },
    },
    orderBy: { createdAt: 'asc' },
  });
  const listOfUser = removeDup(allOrder.map((o) => o.creatorId));
  console.log('🚀 ~ calcTriAnSecondOrder ~ listOfUser:', listOfUser.length);
  for (const userId of listOfUser) {
    const orderOfThisUser = allOrder.filter((o) => o.creatorId === userId);
    console.log(
      '🚀 ~ calcTriAnSecondOrder ~ orderOfThisUser:',
      userId,
      orderOfThisUser.length
    );
    const user = await strapi.db
      .query('plugin::users-permissions.user')
      .findOne({ where: { id: userId } });
    const firstOrder = orderOfThisUser[0];
    if (firstOrder && !firstOrder.firstOrder) {
      // bonus for re-order
      const bonus = Math.floor(firstOrder.finalPrice * CashbackTriAn);
      console.log('🚀 ~ firstOrder ~ bonus:', bonus);
      await addMoneyTo({
        to: userId,
        amount: bonus,
        afterBalance: user.subBalance + bonus,
        type: TransactionType.CashbackTriAn,
        targetWallet: 'sub',
        orderId: firstOrder.id,
        moneySource: user.referCode,
      });
    }

    if (orderOfThisUser.length > 1) {
      for (const secondOrder of orderOfThisUser.slice(1)) {
        const bonus = Math.floor(secondOrder.finalPrice * DiscountSecondOrder);
        console.log('🚀 ~ secondOrder ~ bonus:', bonus);
        await addMoneyTo({
          to: userId,
          amount: bonus,
          afterBalance: user.balance + bonus,
          type: TransactionType.CashbackSecondOrder,
          targetWallet: 'main',
          orderId: secondOrder.id,
          moneySource: user.referCode,
        });
      }
    }
  }
};

const calcTriAnThang1 = async () => {
  // tim cac khoan tien thuong tri an tren 2tr phai nop 15% vao quy PTCD
  const cashInQuyPTCD = await strapi.db
    .query('api::transaction.transaction')
    .findMany({
      where: {
        to: -1,
        type: TransactionType.Withdraw,
      },
    });
  for (const tnx of cashInQuyPTCD) {
    const userId = tnx.from;
    const user = await strapi.db
      .query('plugin::users-permissions.user')
      .findOne({
        where: { id: userId },
      });
    const children = user.sChildren ? user.sChildren.split(',') : [];
    let hasNewF1 = false;
    for (const child of children.reverse()) {
      const childUser = await strapi.db
        .query('plugin::users-permissions.user')
        .findOne({
          where: { id: child },
          populate: ['createdAt'],
        });
      if (dayjs(childUser.createdAt).isAfter(dayjs().startOf('month'))) {
        hasNewF1 = true;
        break;
      }
    }
  }
};

const bonusTet = async () => {
  const orders = [5292, 5293, 5294, 5295, 5297, 5298, 5299, 5300, 5301, 5305];
  orders.forEach(async (o) => {
    const order = await strapi.db
      .query('api::order.order')
      .findOne({ where: { id: o } });
    const user = await strapi.db
      .query('plugin::users-permissions.user')
      .findOne({ where: { id: order.creatorId } });
    const bonus = 50000;
    await addMoneyTo({
      to: order.creatorId,
      amount: bonus,
      afterBalance: user.balance + bonus,
      type: TransactionType.BonusTet,
      targetWallet: 'main',
    });
  });
};

const checkBankTnx = async () => {
  const start = dayjs('00:00 01/02/2024', 'HH:mm DD/MM/YYYY');
  const end = dayjs('00:00 19/02/2024', 'HH:mm DD/MM/YYYY').endOf('day');
  const tnxs = await strapi.db
    .query('api::bank-transaction.bank-transaction')
    .findMany({
      where: {
        createdAt: { $gt: start.toDate(), $lt: end.toDate() },
        // description: { $contains: 'KP' },
      },
    });
  console.log(
    '🚀 ~ checkBankTnx ~ tnxs:',
    tnxs.reduce((a, b) => a + parseInt(b.amount), 0)
  );

  const objectOrderId = {};
  for (const tnx of tnxs) {
    const orderIdStr = tnx.description.split('KP')?.[1];
    if (orderIdStr) {
      const orderId = parseInt(orderIdStr);
      const order = await strapi.db.query('api::order.order').findMany({
        where: { id: orderId },
      });
      console.log(
        'xxxxx',
        orderId,
        order[0].orderStatus,
        tnx.amount,
        order[0].finalPrice,
        tnx.amount == order[0].finalPrice,
        dayjs(order[0].createdAt).isBefore(start)
      );
      objectOrderId[orderId] = true;
    } else {
      console.log('xxxxx notfound ', tnx.amount, tnx.description);
    }
  }

  const orders = await strapi.db.query('api::order.order').findMany({
    where: {
      createdAt: { $gt: start.toDate(), $lt: end.toDate() },
      $or: [
        { orderStatus: OrderStatus.Confirmed },
        { orderStatus: OrderStatus.Shipping },
        { orderStatus: OrderStatus.Success },
      ],
    },
  });
  console.log('======', orders.length);
  // for (const o of orders) {
  //   if (!objectOrderId[o.id]) {
  //     console.log('xxxxx ', o.id, o.finalPrice);
  //   }
  // }
  console.log(
    'total ',
    orders.reduce((a, b) => a + b.finalPrice, 0)
  );
};

const testOrder = async () => {
  const orderData = {
    name: 'Đào Văn Anh',
    phone: '0988070887',
    province: '01',
    district: '001',
    ward: '00008',
    address: '20',
    note: '',
    orderData: {
      items: [
        {
          product: {
            name: 'Sữa Rửa Mặt Tinh Chất Gạo Lebelage Rice Cleansing Foam (Tặng 1 Lọ Nước Hoa 10ml)',
            price: 550000,
            commission: 0,
            discount: 50000,
            description:
              '<p><strong style="-webkit-font-smoothing:antialiased;-webkit-tap-highlight-color:rgba(0, 0, 0, 0);-webkit-text-stroke-width:0px;background-color:rgb(255, 255, 255);box-sizing:inherit;color:rgb(77, 77, 77);font-family:-apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, Ubuntu, &quot;Helvetica Neue&quot;, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;;font-size:16px;font-style:normal;font-variant-caps:normal;font-variant-ligatures:normal;letter-spacing:normal;orphans:2;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-size-adjust:none;text-transform:none;white-space:normal;widows:2;word-spacing:0px;">Sữa rửa mặt gạo Lebelage Rice Cleansing Foam</strong><span style="background-color:rgb(255,255,255);color:rgb(77,77,77);font-family:-apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, Ubuntu, &quot;Helvetica Neue&quot;, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;;font-size:16px;"><span style="-webkit-text-stroke-width:0px;display:inline !important;float:none;font-style:normal;font-variant-caps:normal;font-variant-ligatures:normal;font-weight:400;letter-spacing:normal;orphans:2;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:normal;widows:2;word-spacing:0px;"> chứa chiết xuất từ gạo, giúp làm sạch da nhẹ nhàng đồng thời cấp ẩm, nuôi dưỡng da trắng sáng khỏe mạnh từ bên trong.</span></span><br><br><strong style="-webkit-font-smoothing:antialiased;-webkit-tap-highlight-color:rgba(0, 0, 0, 0);-webkit-text-stroke-width:0px;background-color:rgb(255, 255, 255);box-sizing:inherit;color:rgb(77, 77, 77);font-family:-apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, Ubuntu, &quot;Helvetica Neue&quot;, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;;font-size:16px;font-style:normal;font-variant-caps:normal;font-variant-ligatures:normal;letter-spacing:normal;orphans:2;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-size-adjust:none;text-transform:none;white-space:normal;widows:2;word-spacing:0px;">Loại da phù hợp:</strong><span style="background-color:rgb(255,255,255);color:rgb(77,77,77);font-family:-apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, Ubuntu, &quot;Helvetica Neue&quot;, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;;font-size:16px;"><span style="-webkit-text-stroke-width:0px;display:inline !important;float:none;font-style:normal;font-variant-caps:normal;font-variant-ligatures:normal;font-weight:400;letter-spacing:normal;orphans:2;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:normal;widows:2;word-spacing:0px;"> Da dầu, da hỗn hợp, da thường, da khô.</span></span><br><br><strong style="-webkit-font-smoothing:antialiased;-webkit-tap-highlight-color:rgba(0, 0, 0, 0);-webkit-text-stroke-width:0px;background-color:rgb(255, 255, 255);box-sizing:inherit;color:rgb(77, 77, 77);font-family:-apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, Ubuntu, &quot;Helvetica Neue&quot;, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;;font-size:16px;font-style:normal;font-variant-caps:normal;font-variant-ligatures:normal;letter-spacing:normal;orphans:2;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-size-adjust:none;text-transform:none;white-space:normal;widows:2;word-spacing:0px;">Kết cấu:</strong><span style="background-color:rgb(255,255,255);color:rgb(77,77,77);font-family:-apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, Ubuntu, &quot;Helvetica Neue&quot;, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;;font-size:16px;"><span style="-webkit-text-stroke-width:0px;display:inline !important;float:none;font-style:normal;font-variant-caps:normal;font-variant-ligatures:normal;font-weight:400;letter-spacing:normal;orphans:2;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:normal;widows:2;word-spacing:0px;"> Dạng kem, tạo bọt bông mịn khi dùng.</span></span><br><br><strong style="-webkit-font-smoothing:antialiased;-webkit-tap-highlight-color:rgba(0, 0, 0, 0);-webkit-text-stroke-width:0px;background-color:rgb(255, 255, 255);box-sizing:inherit;color:rgb(77, 77, 77);font-family:-apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, Ubuntu, &quot;Helvetica Neue&quot;, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;;font-size:16px;font-style:normal;font-variant-caps:normal;font-variant-ligatures:normal;letter-spacing:normal;orphans:2;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-size-adjust:none;text-transform:none;white-space:normal;widows:2;word-spacing:0px;">Lợi ích nổi bật</strong><br><span style="background-color:rgb(255,255,255);color:rgb(77,77,77);font-family:-apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, Ubuntu, &quot;Helvetica Neue&quot;, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;;font-size:16px;"><span style="-webkit-text-stroke-width:0px;display:inline !important;float:none;font-style:normal;font-variant-caps:normal;font-variant-ligatures:normal;font-weight:400;letter-spacing:normal;orphans:2;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:normal;widows:2;word-spacing:0px;">- Sữa rửa mặt giúp nhẹ nhàng làm sạch bụi bẩn, dầu nhờn, cặn trang điểm sâu trong lỗ chân lông mà không làm khô căng da.</span></span><br><span style="background-color:rgb(255,255,255);color:rgb(77,77,77);font-family:-apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, Ubuntu, &quot;Helvetica Neue&quot;, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;;font-size:16px;"><span style="-webkit-text-stroke-width:0px;display:inline !important;float:none;font-style:normal;font-variant-caps:normal;font-variant-ligatures:normal;font-weight:400;letter-spacing:normal;orphans:2;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:normal;widows:2;word-spacing:0px;">- Dưỡng da trắng sáng dần dần, loại bỏ làn da xỉn màu nhờ thành phần chiết xuất từ gạo.</span></span><br><span style="background-color:rgb(255,255,255);color:rgb(77,77,77);font-family:-apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, Ubuntu, &quot;Helvetica Neue&quot;, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;;font-size:16px;"><span style="-webkit-text-stroke-width:0px;display:inline !important;float:none;font-style:normal;font-variant-caps:normal;font-variant-ligatures:normal;font-weight:400;letter-spacing:normal;orphans:2;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:normal;widows:2;word-spacing:0px;">- Phục hồi hàng rào độ ẩm của da nhờ các Vitamin A, B, E và khoáng chất giúp da mềm mại, ẩm mượt sau khi dùng.</span></span><br><br><span style="background-color:rgb(255,255,255);color:rgb(77,77,77);font-family:-apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, Ubuntu, &quot;Helvetica Neue&quot;, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;;font-size:16px;"><span style="-webkit-text-stroke-width:0px;display:inline !important;float:none;font-style:normal;font-variant-caps:normal;font-variant-ligatures:normal;font-weight:400;letter-spacing:normal;orphans:2;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:normal;widows:2;word-spacing:0px;">Thương hiệu: </span></span><strong style="-webkit-font-smoothing:antialiased;-webkit-tap-highlight-color:rgba(0, 0, 0, 0);-webkit-text-stroke-width:0px;background-color:rgb(255, 255, 255);box-sizing:inherit;color:rgb(77, 77, 77);font-family:-apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, Ubuntu, &quot;Helvetica Neue&quot;, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;;font-size:16px;font-style:normal;font-variant-caps:normal;font-variant-ligatures:normal;letter-spacing:normal;orphans:2;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-size-adjust:none;text-transform:none;white-space:normal;widows:2;word-spacing:0px;">Lebelage</strong><br><span style="background-color:rgb(255,255,255);color:rgb(77,77,77);font-family:-apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, Ubuntu, &quot;Helvetica Neue&quot;, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;;font-size:16px;"><span style="-webkit-text-stroke-width:0px;display:inline !important;float:none;font-style:normal;font-variant-caps:normal;font-variant-ligatures:normal;font-weight:400;letter-spacing:normal;orphans:2;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:normal;widows:2;word-spacing:0px;">Xuất xứ: Hàn Quốc</span></span></p><p><span style="background-color:rgb(255,255,255);color:rgb(77,77,77);font-family:-apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, Ubuntu, &quot;Helvetica Neue&quot;, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;;font-size:16px;"><span style="-webkit-text-stroke-width:0px;display:inline !important;float:none;font-style:normal;font-variant-caps:normal;font-variant-ligatures:normal;font-weight:400;letter-spacing:normal;orphans:2;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:normal;widows:2;word-spacing:0px;">Dung tích: 100ml</span></span><br>&nbsp;</p>',
            createdAt: '2023-11-24T16:09:22.016Z',
            updatedAt: '2024-02-09T17:39:00.517Z',
            publishedAt: '2024-01-24T05:46:02.642Z',
            hot: 11,
            stock: 135,
            image: [
              'http://127.0.0.1:1337/uploads/small_z5101374268848_43f447165c00288675ad43cad7345edf_5b927dd10f.jpg',
            ],
            product_cats: {
              data: [
                {
                  id: 2,
                  attributes: {
                    name: 'Mỹ phẩm',
                    createdAt: '2023-10-09T09:34:05.881Z',
                    updatedAt: '2023-10-09T09:34:10.089Z',
                    publishedAt: '2023-10-09T09:34:10.084Z',
                  },
                },
              ],
            },
            id: 76,
          },
          quantity: 1,
        },
      ],
      totalPrice: 550000,
      totalDiscount: 50000,
      ship: 0,
      finalPrice: 500000,
      payPrice: 500000,
    },
    paymentMethod: 2,
    finalPrice: 500000,
    payPrice: 500000,
  };
  const creatorId = 79;
  const order = await strapi.db.query('api::order.order').create({
    data: {
      ...orderData,
      orderStatus: OrderStatus.Pending,
      creatorId,
    },
  });
  console.log('🚀 ~ order ~ order:', order);
  const r = await strapi.db.query('api::order.order').update({
    where: { id: order.id },
    data: {
      orderStatus: OrderStatus.Confirmed,
    },
  });
  console.log('🚀 ~ r ~ r:', r);
  const mySale = await strapi.db.query('api::sale.sale').findOne({
    where: { userId: creatorId },
    orderBy: { createdAt: 'desc' },
  });
  console.log('🚀 ~ mySale ~ mySale:', mySale);
  return 'ok';
};

const recalculateTeamSale = async (data) => {
  const { userIds } = data;
  if (Array.isArray(userIds)) {
    for (const userId of userIds) {
      await calculatePersonSale(userId);
    }
  }
};

module.exports = {
  find: async (ctx, next) => {
    try {
      ctx.body = await changeAllUserToUnverified();
    } catch (err) {
      console.log('🚀 ~ file: test-api.js:28 ~ find: ~ err:', err);
      ctx.body = err;
    }
  },
  create: async (ctx, next) => {
    console.log('===> test-api start process ', ctx.request?.body?.method);
    try {
      const { method, data } = ctx.request.body;
      let res = null;
      switch (method) {
        case 'addChildren':
          res = await addChildren(data);
          break;
        case 'copyChildren':
          res = await copyChildren();
          break;
        case 'updateLevel':
          res = await updateLevel();
          break;
        case 'validateChildrenAndParent':
          res = await validateChildrenAndParent();
          break;
        case 'checkSubAccount':
          res = await checkSubAccount();
          break;
        case 'minusBalance':
          res = await minusBalance();
          break;
        case 'checkUserLevel':
          res = await checkUserLevel();
          break;
        case 'resetKyc':
          res = await resetKyc();
          break;
        case 'markDonePayment':
          res = await markDonePayment();
          break;
        case 'updateSaleTable':
          res = await updateSaleTable(data);
          break;
        case 'checkNullQuantity':
          res = await checkNullQuantity();
          break;
        case 'calcBonusPerMonth':
          res = await calcBonusPerMonth();
          break;
        case 'removeCommissionSystem':
          res = await removeCommissionSystem();
          break;
        case 'removeWrongNoti':
          res = await removeWrongNoti();
          break;
        case 'markFirstOrder':
          res = await markFirstOrder();
          break;
        case 'removeUserInTime':
          res = await removeUserInTime();
          break;
        case 'addFieldFirstOrderCreatedAt':
          res = await addFieldFirstOrderCreatedAt();
          break;
        case 'inactiveUserAfterAMonth':
          res = await inactiveUserAfterAMonth();
          break;
        case 'clearSystemWallet':
          res = await clearSystemWallet();
          break;
        case 'expireInactiveUser':
          res = await expireInactiveUser();
          break;
        case 'rearrangeUser':
          res = await rearrangeUser();
          break;
        case 'copyFirstOrderCreatedAt':
          res = await copyFirstOrderCreatedAt();
          break;
        case 'calculateBonusPerMonth':
          res = await calculateBonusPerMonth();
          break;
        case 'printOrder11':
          res = await printOrder11();
          break;
        case 'activeUser':
          res = await activeUser();
          break;
        case 'verifyCommission':
          res = await verifyCommission();
          break;
        case 'latePayment':
          res = await latePayment();
          break;
        case 'fillStatus':
          res = await fillStatus();
          break;
        case 'calcTriAnSecondOrder':
          res = await calcTriAnSecondOrder();
          break;
        case 'calcTriAnThang1':
          res = await calcTriAnThang1();
          break;
        case 'bonusTet':
          res = await bonusTet();
          break;
        case 'withdrawSubWallet':
          res = await withdrawSubWallet();
          break;
        case 'markSubWalletPaymentDone':
          res = await markSubWalletPaymentDone();
          break;
        case 'checkBankTnx':
          res = await checkBankTnx();
          break;
        case 'testOrder':
          res = await testOrder();
          break;
        case 'recalculateTeamSale':
          res = await recalculateTeamSale(data);
          break;
        default:
          break;
      }
      console.log('===> test-api: res: ', res);
      return res;
    } catch (err) {
      ctx.body = err.message;
    }
  },
};
