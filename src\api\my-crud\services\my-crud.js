'use strict';

const {
  getAddressObj,
} = require('../../../plugins/manage-user-tree/utils/CommonUtils');

/**
 * my-crud service
 */

const AcceptedTables = ['address', 'notification'];

module.exports = () => ({
  find: async (creatorId, query, params) => {
    try {
      const { field } = params;

      if (!field || !AcceptedTables.includes(field)) {
        return { message: 'Not Found' };
      }
      query = {
        ...query,
        filters: {
          user: creatorId,
          ...query.filters,
        },
      };

      let data = await strapi.entityService.findMany(
        `api::${field}.${field}`,
        query
      );

      if (field === 'address') {
        data = data.map((address) => {
          const addressObj = getAddressObj(address);
          return {
            ...address,
            addressObj,
          };
        });
      }
      return { data };
    } catch (err) {
      return err;
    }
  },

  findOne: async (creatorId, query, params) => {
    try {
      const { field, id } = params;

      if (!field || !AcceptedTables.includes(field)) {
        return { message: 'Not Found' };
      }

      let data = await strapi.entityService.findOne(
        `api::${field}.${field}`,
        id
      );

      if (field === 'address') {
        data = {
          ...data,
          addressObj: getAddressObj(data),
        };
      }
      return { data };
    } catch (err) {
      return err;
    }
  },

  create: async (params, body, user) => {
    try {
      const { field } = params;
      if (!field || !AcceptedTables.includes(field)) {
        return { message: 'Not Found' };
      }

      const { data } = body;
      if (!data) {
        return { message: `${field} items are empty` };
      }

      const order = await strapi.entityService.create(
        `api::${field}.${field}`,
        {
          data: {
            ...data,
            user: user.id,
          },
        }
      );

      return { data: order };
    } catch (err) {
      return err;
    }
  },

  update: async (params, body, user) => {
    try {
      const { field, id } = params;
      if (!field || !AcceptedTables.includes(field)) {
        return { message: 'Not Found' };
      }

      const { data } = body;
      if (!data) {
        return { message: `${field} items are empty` };
      }

      const order = await strapi.entityService.update(
        `api::${field}.${field}`,
        id,
        {
          data,
        }
      );
      return { data: order };
    } catch (err) {
      return err;
    }
  },

  delete: async (params) => {
    try {
      const { field, id } = params;
      if (!field) {
        return { message: 'Not Found' };
      }

      const order = await strapi.entityService.delete(
        `api::${field}.${field}`,
        id
      );
      return { data: order };
    } catch (err) {
      return err;
    }
  },
});
