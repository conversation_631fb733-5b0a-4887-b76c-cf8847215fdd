'use strict';

/**
 * A set of functions called "actions" for `my-team`
 */

module.exports = {
  find: async (ctx, next) => {
    try {
      const data = await strapi
        .service('api::my-team.my-team')
        .find(ctx.state.user.id, ctx.query);
      ctx.body = data;
    } catch (err) {
      ctx.badRequest('Post report controller error', { moreDetails: err });
    }
  },

  findSub: async (ctx, next) => {
    try {
      const data = await strapi.db
        .query('plugin::users-permissions.user')
        .findMany({
          where: {
            phone: { $contains: `${ctx.state.user.phone}-` },
            blocked: false,
          },
        });
      ctx.body = data;
    } catch (err) {
      ctx.badRequest('Post report controller error', { moreDetails: err });
    }
  },

  join: async (ctx, next) => {
    try {
      const data = await strapi
        .service('api::my-team.my-team')
        .join(ctx.request.body, ctx.state.user);
      ctx.body = data;
    } catch (err) {
      ctx.badRequest('Join team controller error', { moreDetails: err });
    }
  },

  getMember: async (ctx, next) => {
    try {
      const userId = ctx.params.id ? ctx.params.id : ctx.state.user.id;
      const data = await strapi
        .service('api::my-team.my-team')
        .getMember(userId, ctx.query, 0);
      ctx.body = data;
    } catch (err) {
      ctx.badRequest('Find my team controller error', { moreDetails: err });
    }
  },

  getMemberByLevel: async (ctx, next) => {
    try {
      const data = await strapi
        .service('api::my-team.my-team')
        .getMemberByLevel(ctx.state.user.id, ctx.query);
      ctx.body = data;
    } catch (err) {
      ctx.badRequest('Find my team controller error', { moreDetails: err });
    }
  },

  getOrder: async (ctx, next) => {
    try {
      const data = await strapi
        .service('api::my-team.my-team')
        .getOrder(ctx.state.user.id, ctx.query);
      ctx.body = data;
    } catch (err) {
      ctx.badRequest('Get order controller error', { moreDetails: err });
    }
  },

  report: async (ctx, next) => {
    try {
      const data = await strapi
        .service('api::my-team.my-team')
        .report(ctx.state.user.id);
      ctx.body = data;
    } catch (err) {
      ctx.badRequest('Get order controller error', { moreDetails: err });
    }
  },

  findReferCodeOwner: async (ctx, next) => {
    try {
      const data = await strapi
        .service('api::my-team.my-team')
        .findReferCodeOwner(ctx.query);
      ctx.body = data;
    } catch (err) {
      ctx.badRequest('Post report controller error', { moreDetails: err });
    }
  },
};
