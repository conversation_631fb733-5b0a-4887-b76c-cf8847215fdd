module.exports = {
  routes: [
    {
      method: 'GET',
      path: '/my/:field',
      handler: 'my-crud.find',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/my/:field/:id',
      handler: 'my-crud.findOne',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/my/:field',
      handler: 'my-crud.create',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'PUT',
      path: '/my/:field/:id',
      handler: 'my-crud.update',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'DELETE',
      path: '/my/:field/:id',
      handler: 'my-crud.delete',
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
