'use strict';

/**
 * config service
 */

const { createCoreService } = require('@strapi/strapi').factories;

module.exports = createCoreService('api::config.config', ({ strapi }) => ({
  async getAppConfig() {
    const appConfig = await strapi.entityService.findMany('api::config.config');
    console.log('getAppConfig', appConfig);
    ctx.body = appConfig;
  },

  async getFirstComboName() {
    const appConfig = await strapi.entityService.findMany('api::config.config');
    if (!appConfig.FIRST_COMBO_NAME) {
      throw ApplicationError('Missing FIRST_COMBO_NAME config');
    }

    return appConfig.FIRST_COMBO_NAME;
  },
}));
