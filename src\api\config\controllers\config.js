'use strict';

/**
 * config controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::config.config', ({ strapi }) => ({
  async getSystemConfig(ctx) {
    const pluginStore = await strapi.store({
      type: 'plugin',
      name: 'users-permissions',
    });

    const settings = await pluginStore.get({ key: 'advanced' });

    ctx.body = { email_confirmation: settings.email_confirmation };
  },
  async getSystemStatus(ctx) {
    const appConfig = await strapi.entityService.findMany('api::config.config');
    ctx.body = { status: appConfig.content?.status || 'maintain' };
  },
  async getAppConfig(ctx) {
    const appConfig = await strapi.entityService.findMany(
      'api::config.config',
      {
        populate: '*',
      }
    );
    ctx.body = appConfig;
  },
}));
