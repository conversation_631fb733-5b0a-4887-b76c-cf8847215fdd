'use strict';

/**
 * A set of functions called "actions" for `my-transaction`
 */

module.exports = {
  find: async (ctx, next) => {
    try {
      const data = await strapi.entityService.findMany(
        'api::transaction.transaction',
        {
          filters: {
            $or: [{ from: ctx.state.user.id }, { to: ctx.state.user.id }],
          },
          sort: { createdAt: 'desc' },
        }
      );
      ctx.body = data;
    } catch (err) {
      ctx.body = err;
    }
  },
};
