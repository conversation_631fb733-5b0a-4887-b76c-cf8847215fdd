{"kind": "collectionType", "collectionName": "products", "info": {"singularName": "product", "pluralName": "products", "displayName": "Product", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "price": {"type": "integer"}, "bv": {"type": "integer", "required": true}, "image": {"type": "media", "multiple": true, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "commission": {"type": "integer"}, "discount": {"type": "integer"}, "product_cats": {"type": "relation", "relation": "oneToMany", "target": "api::product-cat.product-cat"}, "description": {"type": "customField", "options": {"preset": "toolbar"}, "customField": "plugin::ckeditor5.CKEditor"}, "hot": {"type": "integer", "default": 0}, "stock": {"type": "integer", "min": 0}, "isRecommend": {"type": "boolean", "default": false}, "isBestSeller": {"type": "boolean", "default": false}, "point": {"type": "integer"}, "supplier": {"type": "relation", "relation": "oneToOne", "target": "api::supplier.supplier"}, "isFixedCommission": {"type": "boolean", "default": false}, "promotion": {"type": "relation", "relation": "manyToOne", "target": "api::promotion.promotion", "inversedBy": "products"}, "sold": {"type": "integer"}, "isPaymentBank": {"type": "boolean"}}}