'use strict';

/**
 * transaction-payment service
 */
const { createCoreService } = require('@strapi/strapi').factories;
const { where } = require('lodash/fp');
const { OrderStatus } = require('../../../utils/constants');
const TOKEN_PREFIX = 'Apikey';
const ORDER_CODE_PREFIX = 'STBE';
module.exports = createCoreService(
  'api::transaction-payment.transaction-payment',
  ({ strapi }) => ({
    async createPayment(ctx) {
      try {
        const data = ctx.request.body;
        const { authorization } = ctx.request.header;
        const token = `${TOKEN_PREFIX} ${process.env.STRAPI_ADMIN_ADMIN_SEPAY_API_KEY}`;
        if (authorization !== token) {
          return { success: false, message: 'Unauthorized' };
        }
        const { code, transferAmount, id } = data;
        const paymentTransactionExist = await strapi.db
          .query('api::transaction-payment.transaction-payment')
          .findOne({
            where: {
              id,
            },
          });
        if (paymentTransactionExist) {
          return { success: false, message: 'Đã thanh toán đơn hàng' };
        }

        //create transaction payment

        const orderIdStr = code.replace(ORDER_CODE_PREFIX, '');
        const amount = parseInt(transferAmount);
        const orderId = parseInt(orderIdStr);
        if (amount && orderId) {
          const order = await strapi.entityService.findOne(
            'api::order.order',
            orderId
          );
          const finalPrice = parseInt(order?.finalPrice) || 0;
          const depositValue = parseInt(order?.depositValue) || 0;
          if (
            order &&
            order?.orderStatus === OrderStatus.Pending &&
            (amount === depositValue || amount === finalPrice)
          ) {
            await strapi.entityService.update('api::order.order', orderId, {
              data: {
                orderStatus:
                  amount === finalPrice
                    ? OrderStatus.Confirmed
                    : OrderStatus.Deposited,
              },
            });
            data.order = orderId;
            const paymentTransaction = await strapi.db
              .query('api::transaction-payment.transaction-payment')
              .create({ data });
            return {
              success: true,
              data: paymentTransaction,
            };
          } else {
            return {
              success: false,
              message: 'Thanh toán đơn hàng không hợp lệ',
            };
          }
        } else {
          return { success: false, message: 'Mã đơn hàng không hợp lệ' };
        }
      } catch (error) {
        return {
          success: true,
          message: 'Something went wrong',
        };
      }
    },
  })
);
