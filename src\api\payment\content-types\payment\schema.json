{"kind": "collectionType", "collectionName": "payments", "info": {"singularName": "payment", "pluralName": "payments", "displayName": "Payment", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"receiver": {"type": "integer", "required": true}, "amount": {"type": "biginteger", "required": true}, "week": {"type": "string", "required": true}, "done": {"type": "boolean", "required": true}, "from": {"type": "date", "required": true}, "to": {"type": "date", "required": true}, "wallet": {"type": "string", "default": "main"}}}