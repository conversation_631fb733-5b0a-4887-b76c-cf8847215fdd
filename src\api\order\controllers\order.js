'use strict';

/**
 * order controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::order.order', ({ strapi }) => ({
  async findMy(ctx) {
    ctx.query = {
      ...ctx.query,
      filters: {
        ...ctx.query.filters,
        creator: ctx.state.user.id,
      },
      sort: { createdAt: 'desc' },
    };
    const { data, meta } = await super.find(ctx);
    return { data, meta };
  },
  async findOneMy(ctx) {
    ctx.query = {
      ...ctx.query,
      filters: {
        ...ctx.query.filters,
        creator: ctx.state.user.id,
        id: ctx.params.id,
      },
    };
    const { data } = await super.find(ctx);
    return { data: data[0] || {} };
  },
}));
