{"kind": "collectionType", "collectionName": "bank_transactions", "info": {"singularName": "bank-transaction", "pluralName": "bank-transactions", "displayName": "BankTransaction", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"transactionID": {"type": "string"}, "done": {"type": "boolean"}, "amount": {"type": "string"}, "description": {"type": "text"}, "transactionDate": {"type": "string"}, "type": {"type": "string"}}}