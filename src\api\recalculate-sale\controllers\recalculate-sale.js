'use strict';

const { getFTree } = require('../../../utils/TreeUtils');
const {
  OrderStatus,
  Level1Sale,
  Level,
  LevelByF1,
  MinRevenueForLevel,
} = require('../../../utils/constants');

/**
 * A set of functions called "actions" for `recalculate-sale`
 */

async function recalculatePersonSale(attr) {
  const { id, sale } = attr;
  const orders = await strapi.entityService.findMany('api::order.order', {
    filters: {
      creator: id,
      orderStatus: OrderStatus.Success,
    },
  });
  const mySale = orders.reduce((total, order) => {
    return total + Number(order.finalPrice);
  }, 0);
  if (mySale !== sale) {
    await strapi.db.query('plugin::users-permissions.user').update({
      where: {
        id,
      },
      data: {
        mySale,
      },
    });
    attr.sale = mySale;
  }
}

async function calculateTreeSale(tree) {
  await recalculatePersonSale(tree.attributes);
  for (const child of tree.children) {
    calculateTreeSale(child);
  }
}

async function calculateTreeTeamSale(tree) {
  let total = 0;
  for (const c of tree.children) {
    total += await calculateTreeTeamSale(c);
  }
  if (total !== tree.attributes.teamSale) {
    tree.attributes.teamSale = total;
    await strapi.db.query('plugin::users-permissions.user').update({
      where: { id: tree.attributes.id },
      data: { myTeamSale: total },
    });
  }
  return total + tree.attributes.sale;
}

async function calculateTreeLevel(tree) {
  let level = 0;
  if (tree.attributes.sale >= Level1Sale) {
    level = Level.Membership;
    for (const key in MinRevenueForLevel) {
      if (tree.children.length >= MinRevenueForLevel[key]) {
        level = key;
      }
    }
  }
  if (level !== tree.attributes.level) {
    tree.attributes.level = level;
    await strapi.db.query('plugin::users-permissions.user').update({
      where: { id: tree.attributes.id },
      data: { level },
    });
  }
  for (const child of tree.children) {
    await calculateTreeLevel(child);
  }
}

module.exports = {
  recalculate: async (ctx, next) => {
    try {
      const tree = await getFTree();
      await calculateTreeSale(tree);
      await calculateTreeTeamSale(tree);
      await calculateTreeLevel(tree);
      ctx.body = { success: true, data: tree };
    } catch (err) {
      ctx.body = err;
    }
  },
};
