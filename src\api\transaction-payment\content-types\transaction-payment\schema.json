{"kind": "collectionType", "collectionName": "transaction_payments", "info": {"singularName": "transaction-payment", "pluralName": "transaction-payments", "displayName": "TransactionPayment", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"gateway": {"type": "string"}, "transactionDate": {"type": "datetime"}, "content": {"type": "text"}, "accountNumber": {"type": "string"}, "code": {"type": "string"}, "referenceCode": {"type": "text"}, "description": {"type": "text"}, "order": {"type": "relation", "relation": "oneToOne", "target": "api::order.order"}}}