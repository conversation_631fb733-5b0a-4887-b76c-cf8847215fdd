{"kind": "collectionType", "collectionName": "promotions", "info": {"singularName": "promotion", "pluralName": "promotions", "displayName": "Promotion", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"startDate": {"type": "datetime"}, "endDate": {"type": "datetime"}, "description": {"type": "text"}, "title": {"type": "string"}, "productGift": {"type": "json"}, "products": {"type": "relation", "relation": "oneToMany", "target": "api::product.product", "mappedBy": "promotion"}, "users": {"type": "relation", "relation": "oneToMany", "target": "plugin::users-permissions.user"}}}