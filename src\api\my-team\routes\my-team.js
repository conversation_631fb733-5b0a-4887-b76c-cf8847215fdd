module.exports = {
  routes: [
    {
      method: 'GET',
      path: '/my-team',
      handler: 'my-team.find',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/my-team/sub',
      handler: 'my-team.findSub',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/my-team/join',
      handler: 'my-team.join',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/my-team/member{/:id}?',
      handler: 'my-team.getMember',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/my-team/member-by-level',
      handler: 'my-team.getMemberByLevel',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/my-team/order',
      handler: 'my-team.getOrder',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/my-team/report ',
      handler: 'my-team.report',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/my-team/refer-code',
      handler: 'my-team.findReferCodeOwner',
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
