{"kind": "collectionType", "collectionName": "transactions", "info": {"singularName": "transaction", "pluralName": "transactions", "displayName": "Transaction", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"from": {"type": "integer", "required": true}, "to": {"type": "integer", "required": true}, "amount": {"type": "biginteger", "required": true}, "afterBalance": {"type": "biginteger", "required": true}, "type": {"type": "enumeration", "enum": ["CommissionOrder", "CommissionSystem", "Withdraw", "Manual", "CashbackSecondOrder", "CashbackTriAn", "WithdrawToPTCD", "BonusTet", "BonusForParentSecondOrder"]}, "moneySource": {"type": "string"}, "targetWallet": {"type": "enumeration", "enum": ["main", "sub"]}, "orderId": {"type": "string"}}}