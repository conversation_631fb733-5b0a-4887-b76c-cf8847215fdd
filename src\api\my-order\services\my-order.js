'use strict';

const utils = require('@strapi/utils');
const { ApplicationError, ValidationError } = utils.errors;

const {
  OrderStatus,
  OrderShipStatus,
  VoucherType,
  LevelBv,
  OrderType,
} = require('../../../utils/constants');
const { isChildOf } = require('../../../utils/utils');
const {
  formatPrice,
} = require('../../../plugins/manage-user-tree/utils/CommonUtils');
const CryptoJS = require('crypto-js');

/**
 * my-order service
 */

module.exports = () => ({
  find: async (creator, query) => {
    try {
      let filters = {};
      if (!query?.filters) {
        filters.creator = creator;
      } else {
        query = {
          ...query,
          filters: {
            creator,
            ...query.filters,
          },
        };
      }
      const data = await strapi.entityService.findMany(
        'api::order.order',
        query
      );
      return { data };
    } catch (err) {
      return err;
    }
  },
  findOne: async (creator, query, params) => {
    try {
      const { id } = params;
      query = {
        ...query,
        filters: {
          ...query.filters,
          id,
        },
        populate: {
          creator: true,
        },
      };
      const data = await strapi.entityService.findMany(
        'api::order.order',
        query
      );
      if (
        data?.[0]?.creator?.id === creator ||
        isChildOf(data?.[0]?.creator?.id, creator)
      ) {
        return { data: data?.[0] };
      } else {
        return {};
      }
    } catch (err) {
      return err;
    }
  },
  create: async (body, user) => {
    try {
      const { data } = body;
      const { orderData, finalPrice, voucherId, type } = data;
      let checkFinalPrice = 0;
      if (type == OrderType.COURSE) {
        if (orderData.items.length === 0) {
          return { message: 'Order items are empty' };
        }
        for (const item of orderData.items) {
          const courseId = item.product.id;
          let p;
          p = await strapi.entityService.findOne(
            'api::course.course',
            courseId
          );
          if (!p) {
            return { message: 'Course not found' };
          }
          if (
            p.price !== item.product.price ||
            p.discount !== item.product.discount
          ) {
            return { message: 'Course price mismatch' };
          }
          checkFinalPrice += item.product.price - item.product.discount;
        }
      } else {
        if (orderData.items.length === 0) {
          return { message: 'Order items are empty' };
        }
        for (const item of orderData.items) {
          const productId = item.product.id;
          const p = await strapi.entityService.findOne(
            'api::product.product',
            productId
          );
          if (!p) {
            return { message: 'Product not found' };
          }
          if (
            p.price !== item.product.price ||
            p.discount !== item.product.discount ||
            p.bv !== item.product.bv
          ) {
            return { message: 'Product price mismatch' };
          }
          const stock = p.stock || 0;
          if (stock < item.quantity) {
            return {
              message: 'Insufficient stock',
              data: { productId, quantity: stock },
            };
          }
          const discountedPrice = item.product.price - item.product.discount;
          checkFinalPrice +=
            (discountedPrice -
              (item.product.bv * discountedPrice * LevelBv[user?.level || 0]) /
                100) *
            item.quantity;
        }
        checkFinalPrice += orderData.ship || 0;

        /**
         * check voucher
         */
        if (voucherId) {
          const voucher = await strapi.db
            .query('api::voucher.voucher')
            .findOne({
              where: {
                $and: [
                  {
                    id: voucherId,
                  },
                  {
                    users: {
                      id: {
                        $eq: user.id,
                      },
                    },
                  },
                ],
              },
              populate: ['users'],
            });
          if (!voucher) {
            return {
              message: 'Voucher is not valid or user does not have voucher',
            };
          }
          // Check expired of voucher
          if (
            voucher.expiredDate &&
            new Date(voucher.expiredDate).getTime() < new Date().getTime()
          ) {
            throw new ApplicationError('This voucher has expired');
          }

          if (voucher?.type) {
            switch (voucher.type) {
              case VoucherType.DiscountPercent: {
                if (voucher.discountValue) {
                  let discountPrice =
                    (checkFinalPrice * voucher.discountValue) / 100;

                  // If existed max price and discount price is more than max price, get max price
                  if (voucher.maxPrice && discountPrice > voucher.maxPrice) {
                    discountPrice = voucher.maxPrice;
                  }

                  checkFinalPrice = checkFinalPrice - discountPrice;
                }
                break;
              }
              case VoucherType.DiscountPrice: {
                if (voucher.discountValue) {
                  // Having min price (min price to apply voucher)
                  if (voucher.minPrice) {
                    if (orderData.finalPrice < voucher.minPrice) {
                      throw new ApplicationError('Voucher is not valid');
                    }
                  }
                  checkFinalPrice = checkFinalPrice - voucher.discountValue;
                }
                break;
              }
              default:
                break;
            }
          }

          // Check quantity of voucher
          if (!voucher?.quantity) {
            throw new ApplicationError(
              'The quantity of vouchers has been depleted'
            );
          } else {
            // Reduce voucher when user use it
            await strapi.entityService.update(
              'api::voucher.voucher',
              voucherId,
              {
                data: {
                  quantity: voucher.quantity - 1,
                  users: voucher.users.filter((e) => e.id !== user.id),
                },
              }
            );
          }
        }
      }
      // verify order items price

      if (checkFinalPrice !== finalPrice) {
        return { message: 'Final price mismatch' };
      }

      const order = await strapi.entityService.create('api::order.order', {
        data: {
          ...data,
          finalPrice: Math.round(Number(data.finalPrice)),
          orderStatus: OrderStatus.Pending,
          creator: user.id,
          type: type ?? OrderType.PRODUCT,
        },
      });
      return { data: order };
    } catch (err) {
      console.log('🚀 ~ file: my-order.js:58 ~ create: ~ err:', err);
      return err;
    }
  },
  cancel: async (body, user) => {
    try {
      if (body.orderId) {
        const order = await strapi.entityService.findOne(
          'api::order.order',
          body.orderId
        );
        if (
          order &&
          order.creatorId === user.id &&
          order.orderStatus === OrderStatus.Pending
        ) {
          const res = await strapi.entityService.update(
            'api::order.order',
            order.id,
            {
              data: {
                orderStatus: OrderStatus.Cancelled,
              },
            }
          );
          return res;
        }
      }
      return { success: false };
    } catch (err) {
      console.log('🚀 ~ file: my-order.js:58 ~ create: ~ err:', err);
      return err;
    }
  },
  ship: async (body) => {
    if (!Object.values(OrderShipStatus).includes(body.Status)) {
      return { code: 200 };
    }
    try {
      const order = await strapi.db.query('api::order.order').findOne({
        where: { orderCode: body.OrderCode },
      });
      if (order) {
        let newOrderStatus = order.orderStatus;
        switch (body.Status) {
          case OrderShipStatus.Delivering:
            newOrderStatus = OrderStatus.Shipping;
            break;
          case OrderShipStatus.Delivered:
            newOrderStatus = OrderStatus.Success;
            break;
          default:
            newOrderStatus = OrderStatus.Cancelled;
            break;
        }
        await strapi.entityService.update('api::order.order', order.id, {
          data: {
            orderStatus: newOrderStatus,
            orderData: {
              ...order.orderData,
              ship: {
                ...order.orderData.ship,
                result: !order.orderData.ship.result
                  ? [body]
                  : [...order.orderData.ship.result, body],
              },
            },
          },
        });
      }
      return { code: 200 };
    } catch (err) {
      console.log('🚀 ~ file: my-order.js:151 ~ ship: ~ err:', err);
    }
  },
  createMac: async (body) => {
    try {
      console.log('🚀 ~ getMac: ~ body:', body);
      const dataMac = Object.keys(body)
        .sort() // sắp xếp key của Object data theo thứ tự từ điển tăng dần
        .map(
          (key) =>
            `${key}=${
              typeof body[key] === 'object'
                ? JSON.stringify(body[key])
                : body[key]
            }`
        ) // trả về mảng dữ liệu dạng [{key=value}, ...]
        .join('&');
      const mac = CryptoJS.HmacSHA256(
        dataMac,
        process.env.STRAPI_ADMIN_ZALO_CHECKOUT_SECRET_KEY
      ).toString();
      return mac;
    } catch (e) {
      console.log('🚀 ~ getMac: ~ e:', e);
    }
  },
  zaloNotify: async (body) => {
    try {
      console.log('🚀 ~ getMac: ~ body:', body);
      const { data, mac } = body || {};
      if (!data || !mac) {
        return {
          returnCode: 0,
          returnMessage: 'Missing data or mac',
        };
      }
      const { method, orderId, appId } = data || {};
      if (!method || !orderId || !appId) {
        return {
          returnCode: 0,
          returnMessage: 'Missing method or orderId or appId',
        };
      }
      const str = `appId=${appId}&orderId=${orderId}&method=${method}`;
      const reqmac = CryptoJS.HmacSHA256(
        str,
        process.env.STRAPI_ADMIN_ZALO_CHECKOUT_SECRET_KEY
      ).toString();
      console.log('🚀 ~ zaloNotify: ~ reqmac:', reqmac);
      if (reqmac == mac) {
        return {
          returnCode: 1,
          returnMessage: 'Success',
        };
      } else {
        return {
          returnCode: 0,
          returnMessage: 'Fail',
        };
      }
    } catch (e) {
      console.log('🚀 ~ getMac: ~ e:', e);
    }
  },
  checkoutCallback: async (body) => {
    /* callback Zalo Checkoutsdk trả về sau khi thanh toán VNPay xong
    {
      "data": {
        "amount": 568000,
        "method": "VNPAY_SANDBOX",
        "orderId": "221720729763810016918725488_1717548385835",
        "transId": "240605_0746258352773438459351224118090",
        "appId": "1666682950662020156",
        "extradata": "%7B%22storeName%22%3A%22Kho%20t%E1%BB%95ng%22%2C%22storeId%22%3A%221%22%2C%22orderId%22%3A87%2C%22notes%22%3Anull%7D",
        "resultCode": 1,
        "description": "Thanh%20to%C3%A1n%20568.000%C4%91",
        "message": "Giao dịch thành công",
        "paymentChannel": "VNPAY_SANDBOX"
      },
      "overallMac": "119033ee924d493932522b9a7399a4a015e1507c9d9e53a0c618ac9142d635a5",
      "mac": "a57f1a3669c64817a695bb41dca80de2e2eb91999b8bcea6a646eb9468774567"
    }
    */
    try {
      const { data, overallMac, mac } = body || {};
      await strapi.db.query('api::request-log.request-log').create({
        data: {
          response: body,
          url: data.method?.includes('VNPAY') ? 'IPN VNPAY' : 'IPN COD',
        },
      });
      if (!data || !overallMac || !mac) {
        return {
          returnCode: 0,
          returnMessage: 'Missing data or mac',
        };
      }
      const {
        method,
        orderId,
        appId,
        amount,
        description,
        message,
        resultCode,
        extradata,
        transId,
      } = data || {};
      if (!method || !orderId || !appId || !extradata) {
        return {
          returnCode: 0,
          returnMessage: 'Missing method or orderId or appId',
        };
      }
      const str = `appId=${appId}&amount=${amount}&description=${description}&orderId=${orderId}&message=${message}&resultCode=${resultCode}&transId=${transId}`;
      const reqmac = CryptoJS.HmacSHA256(
        str,
        process.env.STRAPI_ADMIN_ZALO_CHECKOUT_SECRET_KEY
      ).toString();
      console.log('reqmac is', reqmac);
      const validMac = reqmac === mac;

      const dataOverallMac = Object.keys(data)
        .sort()
        .map((key) => `${key}=${data[key]}`)
        .join('&');
      const reqOverallMac = CryptoJS.HmacSHA256(
        dataOverallMac,
        process.env.STRAPI_ADMIN_ZALO_CHECKOUT_SECRET_KEY
      ).toString();
      const validOverallMac = overallMac === reqOverallMac;
      if (validOverallMac && validMac) {
        const extraData = decodeURIComponent(extradata);
        const extraDataObj = JSON.parse(extraData);
        if (extraDataObj?.orderId) {
          await strapi.db.query('api::order.order').update({
            where: {
              id: extraDataObj?.orderId,
            },
            data: {
              orderStatus: OrderStatus.Confirmed,
              paymentMethod: PaymentMethod.COD,
              paymentMethodResponse: data,
            },
          });
          await strapi
            .service('plugin::manage-order.myService')
            .createShip(extraDataObj?.orderId);
          return {
            returnCode: 1,
            returnMessage: 'Success',
          };
        }
      }
      return {
        returnCode: 0,
        returnMessage: 'Fail',
      };
    } catch (e) {
      console.log('🚀 ~ checkoutCallback: ~ e:', e);
    }
  },
});
