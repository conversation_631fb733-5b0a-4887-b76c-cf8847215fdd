{"kind": "collectionType", "collectionName": "warehouses", "info": {"singularName": "warehouse", "pluralName": "warehouses", "displayName": "Warehouse", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"code": {"type": "string", "required": true, "unique": true}, "name": {"type": "string", "required": true, "unique": true}, "address": {"type": "string", "required": true}}}