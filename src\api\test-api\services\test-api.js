'use strict';

/**
 * test-api service
 */

module.exports = () => ({
  find: async () => {
    try {
      console.log('🚀 ~ file: test-api.js:13 ~ find');

      const res = await strapi.db.connection.context.raw(
        `UPDATE 'sqlite_sequence'
        SET 'seq' = (SELECT MAX('id') FROM 'order_statuses')
        WHERE 'name' = 'order_statuses';`
      );
      console.log('🚀 ~ file: test-api.js:13 ~ find: ~ res:', res);
      return { data: 'success' };
    } catch (err) {
      console.log('🚀 ~ file: test-api.js:16 ~ find: ~ err:', err);
      return err;
    }
  },
});
/**
 * select name from sqlite_master where type='table'
 * select * from order_statuses
 *
 * view sequence: SELECT * FROM 'sqlite_sequence'
 * UPDATE 'sqlite_sequence'
        SET 'seq' = (SELECT MAX('id') FROM 'order_statuses')
        WHERE 'name' = 'order_statuses';
 */
