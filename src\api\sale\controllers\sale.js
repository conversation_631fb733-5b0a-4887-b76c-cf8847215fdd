'use strict';

/**
 * sale controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::sale.sale', () => ({
  async findMy(ctx) {
    ctx.query = {
      ...ctx.query,
      filters: {
        ...ctx.query.filters,
        userId: ctx.state.user.id,
      },
    };
    const data = await strapi.entityService.findMany(
      'api::sale.sale',
      ctx.query
    );
    const sanitizedResults = await this.sanitizeOutput(data, ctx);
    return this.transformResponse(sanitizedResults);
  },
}));
