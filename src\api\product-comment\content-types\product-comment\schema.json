{"kind": "collectionType", "collectionName": "product_comments", "info": {"singularName": "product-comment", "pluralName": "product-comments", "displayName": "ProductComment"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"content": {"type": "string"}, "rate": {"type": "float"}, "user": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "product": {"type": "relation", "relation": "oneToOne", "target": "api::product.product"}}}