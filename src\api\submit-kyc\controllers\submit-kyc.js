'use strict';

/**
 * A set of functions called "actions" for `submit-kyc`
 */
const { parseMultipartData } = require('@strapi/utils');
const { GeneralNoti } = require('../../../utils/constants');
const { trackEvent, indexOfUserNP } = require('../../../utils/utils');

const updateVerified = async (userId) => {
  const res2 = await strapi
    .service('api::identity-verification.identity-verification')
    .find({
      filters: {
        userId,
      },
      populate: '*',
    });
  const {
    identityNumber,
    accountName,
    frontIdentityCard,
    backIdentityCard,
    accountNumber,
    bank,
  } = res2?.results?.[0] || {};
  if (
    identityNumber &&
    accountName &&
    frontIdentityCard &&
    backIdentityCard &&
    accountNumber &&
    bank
  ) {
    await strapi.entityService.update(
      'plugin::users-permissions.user',
      userId,
      {
        data: {
          verified: true,
        },
      }
    );
    await strapi.query('api::notification.notification').create({
      data: {
        user: userId,
        content: GeneralNoti.NewUserVerified,
      },
    });
  }
};

module.exports = {
  create: async (ctx, next) => {
    try {
      if (ctx.is('multipart')) {
        const { data, files } = parseMultipartData(ctx);
        const existed = await strapi.entityService.findMany(
          'api::identity-verification.identity-verification',
          {
            filters: {
              userId: ctx.state.user.id,
            },
          }
        );
        if (data.name) {
          const res1 = await strapi.db
            .query('plugin::users-permissions.user')
            .update({
              where: { id: ctx.state.user.id },
              data: { name: data.name },
            });
          trackEvent('submit-kyc-create', {
            id: ctx.state.user.id,
            name: data.name,
            res1,
          });
        }
        if (existed.length > 0) {
          const res = await strapi
            .service('api::identity-verification.identity-verification')
            .update(existed[0].id, {
              data: {
                identityNumber: data.identityNumber,
                accountNumber: data.accountNumber,
                bank: data.bank,
                accountName: data.accountName,
                bankBranch: data.bankBranch,
                issueDate: data.issueDate,
                issuePlace: data.issuePlace,
                bankProvince: data.bankProvince,
              },
              files,
            });
          res.id && (await updateVerified(ctx.state.user.id));
          ctx.body = res;
        } else {
          const res = await strapi
            .service('api::identity-verification.identity-verification')
            .create({
              data: {
                userId: ctx.state.user.id,
                phone: ctx.state.user.phone,
                identityNumber: data.identityNumber,
                accountNumber: data.accountNumber,
                bank: data.bank,
                accountName: data.accountName,
                bankBranch: data.bankBranch,
                issueDate: data.issueDate,
                issuePlace: data.issuePlace,
                bankProvince: data.bankProvince,
              },
              files,
            });
          res.id && (await updateVerified(ctx.state.user.id));
          ctx.body = res;
        }
      }
    } catch (err) {
      ctx.body = err;
    }
  },
  find: async (ctx, next) => {
    try {
      const res = await strapi.db
        .query('api::identity-verification.identity-verification')
        .findOne({
          where: { userId: ctx.state.user.id },
          populate: true,
        });
      // const indexOfUser = await strapi.db
      //   .query('plugin::users-permissions.user')
      //   .count({
      //     orderBy: { createdAt: 'desc' },
      //     where: {
      //       createdAt: { $lt: ctx.state.user.createdAt },
      //       blocked: false,
      //     },
      //   });
      // console.log('🚀 ~ find: ~ indexOfUser:', indexOfUser);
      const indexOfUser = await indexOfUserNP(ctx.state.user);
      ctx.body = { ...res, indexOfUser };
    } catch (err) {
      ctx.body = err;
    }
  },
};

// const a = {
//   name: 0,
//   children: [
//     {
//       name: 1,
//       left: true,
//       children: [
//         {
//           name: 3,
//           left: true,
//           children: [
//             {
//               name: 7,
//               left: true,
//               children: [],
//             },
//             {
//               name: 11,
//               right: true,
//               children: [],
//             },
//           ],
//         },
//         {
//           name: 5,
//           right: true,
//           children: [
//             {
//               name: 9,
//               left: true,
//               children: [],
//             },
//             {
//               name: 13,
//               right: true,
//               children: [],
//             },
//           ],
//         },
//       ],
//     },
//     {
//       name: 2,
//       right: true,
//       children: [
//         {
//           name: 4,
//           left: true,
//           children: [
//             {
//               name: 8,
//               left: true,
//               children: [],
//             },
//             {
//               name: 12,
//               right: true,
//               children: [],
//             },
//           ],
//         },
//         {
//           name: 6,
//           right: true,
//           children: [
//             {
//               name: 10,
//               left: true,
//               children: [],
//             },
//             {
//               name: 14,
//               right: true,
//               children: [],
//             },
//           ],
//         },
//       ],
//     },
//   ],
// };
