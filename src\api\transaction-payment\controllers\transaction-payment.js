'use strict';

/**
 * transaction-payment controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::transaction-payment.transaction-payment',({ strapi }) => ({
  async createPayment(ctx) {
    const transactionPayment = await strapi
    .service("api::transaction-payment.transaction-payment").createPayment(ctx);
    ctx.body = transactionPayment;
  }
}));
