{"kind": "collectionType", "collectionName": "up_users", "info": {"name": "user", "description": "", "singularName": "user", "pluralName": "users", "displayName": "User"}, "options": {"draftAndPublish": false}, "attributes": {"provider": {"type": "string", "configurable": false}, "password": {"type": "password", "minLength": 6, "configurable": false, "private": true, "searchable": false}, "resetPasswordToken": {"type": "string", "configurable": false, "private": true, "searchable": false}, "confirmationToken": {"type": "string", "configurable": false, "private": true, "searchable": false}, "confirmed": {"type": "boolean", "default": false, "configurable": false}, "blocked": {"type": "boolean", "default": false, "configurable": false}, "role": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.role", "inversedBy": "users", "configurable": false}, "phone": {"type": "string", "minLength": 10, "required": true, "unique": true, "maxLength": 30}, "referCode": {"type": "string", "required": false, "unique": false}, "addressId": {"type": "integer"}, "addresses": {"type": "relation", "relation": "oneToMany", "target": "api::address.address", "mappedBy": "user"}, "province": {"type": "string"}, "district": {"type": "string"}, "ward": {"type": "string"}, "name": {"type": "text"}, "balance": {"type": "biginteger", "required": true}, "mySale": {"type": "biginteger"}, "fParent": {"type": "integer"}, "fChildren": {"type": "relation", "relation": "oneToMany", "target": "plugin::users-permissions.user"}, "myTeamSale": {"type": "biginteger"}, "level": {"type": "integer"}, "avatar": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "verified": {"type": "boolean"}, "numberOfSubAccount": {"type": "integer"}, "subBalance": {"type": "biginteger", "required": false}, "sChildren": {"type": "text"}, "firstOrderCreatedAt": {"type": "datetime"}, "status": {"type": "enumeration", "enum": ["inactive", "active", "reactive"]}, "firstOrderAt": {"type": "datetime"}, "taxCode": {"type": "string"}, "emailAddress": {"type": "string"}, "dob": {"type": "string"}, "avatarUrl": {"type": "string"}, "updatedReferCode": {"type": "boolean", "default": false}, "zaloId": {"type": "string"}, "email": {"type": "string"}, "address": {"type": "string"}, "extra": {"type": "json"}, "rateCommissionPartner": {"type": "float"}, "colabStatus": {"type": "enumeration", "enum": ["Not waiting for A<PERSON><PERSON>", "Waiting for Approve", "Approved"], "default": "Not waiting for A<PERSON><PERSON>"}, "commission": {"type": "json"}, "username": {"type": "string"}, "warehouse": {"type": "integer"}, "isZaloOA": {"type": "boolean"}, "bonusPoint": {"type": "integer"}, "vouchers": {"type": "relation", "relation": "manyToMany", "target": "api::voucher.voucher", "inversedBy": "users"}, "bankInfo": {"type": "json"}, "isBuyFirstCombo": {"type": "boolean"}, "cccd": {"type": "string"}, "khoa_hoc": {"type": "relation", "relation": "manyToOne", "target": "api::course.course", "inversedBy": "users"}}}