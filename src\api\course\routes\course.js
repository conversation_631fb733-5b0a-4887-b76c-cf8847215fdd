module.exports = {
  routes: [
    {
      method: 'GET',
      path: '/courses/list',
      handler: 'course.find',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/courses/my',
      handler: 'course.getMyCourse',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/courses/enroll-free',
      handler: 'course.enrollFreeCourse',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/courses/:id',
      handler: 'course.getOne',
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
