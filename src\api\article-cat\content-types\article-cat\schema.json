{"kind": "collectionType", "collectionName": "article_cats", "info": {"singularName": "article-cat", "pluralName": "article-cats", "displayName": "ArticleCat", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "articles": {"type": "relation", "relation": "manyToMany", "target": "api::article.article", "mappedBy": "article_cats"}}}