{"kind": "collectionType", "collectionName": "course_cats", "info": {"singularName": "course-cat", "pluralName": "course-cats", "displayName": "<PERSON><PERSON> mụ<PERSON> kh<PERSON><PERSON> học", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "courses": {"type": "relation", "relation": "oneToMany", "target": "api::course.course", "mappedBy": "course_cat"}, "image": {"allowedTypes": ["images", "files", "videos", "audios"], "type": "media", "multiple": false}}}