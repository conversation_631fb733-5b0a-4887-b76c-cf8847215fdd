module.exports = {
  routes: [
    {
      method: 'GET',
      path: '/my-order',
      handler: 'my-order.find',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/my-order/:id',
      handler: 'my-order.findOne',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/my-order',
      handler: 'my-order.create',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/my-order/cancel',
      handler: 'my-order.cancel',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/my-order/ship',
      handler: 'my-order.ship',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/my-order/mac',
      handler: 'my-order.createMac',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/my-order/zalo-notify',
      handler: 'my-order.zaloNotify',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/my-order/checkout-callback',
      handler: 'my-order.checkoutCallback',
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
