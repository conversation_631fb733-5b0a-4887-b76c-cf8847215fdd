'use strict';

/**
 * A set of functions called "actions" for `address-config`
 */

module.exports = {
  getProvince: async (ctx, next) => {
    try {
      const data = await strapi
        .service('api::address-config.address-config')
        .getProvince(ctx.query);
      ctx.body = data;
    } catch (err) {
      ctx.badRequest('Post report controller error', { moreDetails: err });
    }
  },
  getDistrict: async (ctx, next) => {
    try {
      const data = await strapi
        .service('api::address-config.address-config')
        .getDistrict(ctx.query);
      ctx.body = data;
    } catch (err) {
      ctx.badRequest('Post report controller error', { moreDetails: err });
    }
  },
  getWard: async (ctx, next) => {
    try {
      const data = await strapi
        .service('api::address-config.address-config')
        .getWard(ctx.query);
      ctx.body = data;
    } catch (err) {
      ctx.badRequest('Post report controller error', { moreDetails: err });
    }
  },
  getBank: async (ctx, next) => {
    try {
      const data = await strapi
        .service('api::address-config.address-config')
        .getBank(ctx.query);
      ctx.body = data;
    } catch (err) {
      ctx.badRequest('Post report controller error', { moreDetails: err });
    }
  },
};
