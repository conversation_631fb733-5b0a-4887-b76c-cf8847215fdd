module.exports = {
  routes: [
    {
      method: 'POST',
      path: '/send-otp',
      handler: 'send-otp.send',
      config: {
        policies: [],
        middlewares: ['api::send-otp.otp-middleware'],
      },
    },
    {
      method: 'POST',
      path: '/send-otp/verify-register',
      handler: 'send-otp.verify',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/send-otp/forgot',
      handler: 'send-otp.forgot',
      config: {
        policies: [],
        middlewares: ['api::send-otp.otp-middleware'],
      },
    },
    {
      method: 'POST',
      path: '/send-otp/verify-forgot',
      handler: 'send-otp.verifyForgot',
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
