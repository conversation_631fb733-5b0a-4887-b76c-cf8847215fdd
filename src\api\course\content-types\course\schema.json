{"kind": "collectionType", "collectionName": "courses", "info": {"singularName": "course", "pluralName": "courses", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "price": {"type": "integer"}, "discount": {"type": "integer"}, "description": {"type": "customField", "options": {"preset": "toolbar"}, "customField": "plugin::ckeditor5.CKEditor"}, "image": {"type": "media", "multiple": true, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "course_cat": {"type": "relation", "relation": "manyToOne", "target": "api::course-cat.course-cat", "inversedBy": "courses"}, "lessons": {"type": "relation", "relation": "oneToMany", "target": "api::lesson.lesson", "mappedBy": "course"}, "users": {"type": "relation", "relation": "oneToMany", "target": "plugin::users-permissions.user", "mappedBy": "khoa_hoc"}, "isFeatured": {"type": "boolean"}}}