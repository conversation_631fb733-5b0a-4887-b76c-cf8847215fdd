{"kind": "collectionType", "collectionName": "lessons", "info": {"singularName": "lesson", "pluralName": "lessons", "displayName": "<PERSON><PERSON><PERSON>", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"lession_number": {"type": "integer", "required": true}, "title": {"type": "string", "required": true}, "description": {"type": "customField", "options": {"preset": "toolbar"}, "customField": "plugin::ckeditor5.CKEditor"}, "main_video": {"type": "media", "multiple": false, "required": true, "private": true, "allowedTypes": ["videos"]}, "course": {"type": "relation", "relation": "manyToOne", "target": "api::course.course", "inversedBy": "lessons"}}}