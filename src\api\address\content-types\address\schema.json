{"kind": "collectionType", "collectionName": "addresses", "info": {"singularName": "address", "pluralName": "addresses", "displayName": "Address", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"address": {"type": "string", "required": true}, "note": {"type": "string"}, "province": {"type": "string", "required": true}, "district": {"type": "string", "required": true}, "ward": {"type": "string", "required": true}, "userName": {"type": "string", "required": true}, "phone": {"type": "string", "required": true}, "user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "addresses"}}}