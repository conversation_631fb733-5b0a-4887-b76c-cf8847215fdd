'use strict';

/**
 * A set of functions called "actions" for `my-crud`
 */

module.exports = {
  find: async (ctx, next) => {
    try {
      const data = await strapi
        .service('api::my-crud.my-crud')
        .find(ctx.state.user.id, ctx.query, ctx.params);
      ctx.body = data;
    } catch (err) {
      ctx.badRequest('Post report controller error', { moreDetails: err });
    }
  },

  findOne: async (ctx, next) => {
    try {
      const data = await strapi
        .service('api::my-crud.my-crud')
        .findOne(ctx.state.user.id, ctx.query, ctx.params);
      ctx.body = data;
    } catch (err) {
      ctx.badRequest('Post report controller error', { moreDetails: err });
    }
  },

  create: async (ctx, next) => {
    try {
      const data = await strapi
        .service('api::my-crud.my-crud')
        .create(ctx.params, ctx.request.body, ctx.state.user);
      ctx.body = data;
    } catch (err) {
      ctx.badRequest('Post report controller error', { moreDetails: err });
    }
  },

  update: async (ctx, next) => {
    try {
      const data = await strapi
        .service('api::my-crud.my-crud')
        .update(ctx.params, ctx.request.body, ctx.state.user);
      ctx.body = data;
    } catch (err) {
      ctx.badRequest('Post report controller error', { moreDetails: err });
    }
  },

  delete: async (ctx, next) => {
    try {
      const data = await strapi
        .service('api::my-crud.my-crud')
        .delete(ctx.params);
      ctx.body = data;
    } catch (err) {
      ctx.badRequest('Post report controller error', { moreDetails: err });
    }
  },
};
