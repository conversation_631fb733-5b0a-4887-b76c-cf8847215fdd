'use strict';

/**
 * product service
 */

const { createCoreService } = require('@strapi/strapi').factories;

module.exports = createCoreService('api::product.product', ({ strapi }) => ({
  async listProduct(query) {
    let queryList = `SELECT p.* FROM products p`;
    if (query.filters) {
      let queryListFilter = [];
      if (query.filters.search) {
        queryListFilter.push(
          `(unaccent(p.name) ILIKE '%${query.filters.search}%'  Or p.name ILIKE '%${query.filters.search}%' )`
        );
      }
      if (queryListFilter.length) {
        queryList += ' WHERE ' + queryListFilter.join(' AND ');
      }
    }
    queryList += query.filters?.search
      ? ' AND p.published_at is not null'
      : ' WHERE p.published_at is not null';

    // console.log('~~~queryList', queryList);
    const dataPromise = await strapi.db.connection.raw(queryList);
    const listIdProduct = dataPromise.rows.map((i) => i.id);

    let conditions = {
      id: listIdProduct,
    };
    if (query?.filters?.category) {
      conditions = {
        ...conditions,
        product_cats: {
          id: query.filters.category,
        },
      };
    }
    const queryListProduct = {
      filters: conditions,
      populate: {
        image: true,
        product_cats: true,
      },
      page: query?.pagination?.page || 1,
      pageSize: query?.pagination?.pageSize || 10,
    };
    const listProducts = await strapi.entityService.findPage(
      'api::product.product',
      queryListProduct
    );
    return {
      data: listProducts.results,
      meta: listProducts.pagination,
    };
  },
}));
