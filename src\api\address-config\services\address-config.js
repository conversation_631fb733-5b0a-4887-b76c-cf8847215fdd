'use strict';

const bank = require('../../../utils/provinces/bank');
const district = require('../../../utils/provinces/district');
const province = require('../../../utils/provinces/province');
const ward = require('../../../utils/provinces/ward');

/**
 * address-config service
 */

module.exports = () => ({
  getProvince: async (query) => {
    const { code } = query;
    if (code) {
      return province.find((item) => item.code === query.code);
    } else return province;
  },
  getDistrict: async (query) => {
    const { code, parent_code } = query;
    if (code) {
      return district.find((item) => item.code === query.code);
    } else if (parent_code) {
      return district.filter((item) => item.parent_code === parent_code);
    } else {
      return district;
    }
  },
  getWard: async (query) => {
    try {
      const { code, parent_code } = query;
      if (code) {
        return ward.find((item) => item.code === query.code);
      } else if (parent_code) {
        return ward.filter((item) => item.parent_code === parent_code);
      } else {
        return ward;
      }
    } catch (error) {}
  },
  getBank: async (query) => {
    const { shortName } = query;
    if (shortName) {
      return bank.find((item) => item.shortName === query.shortName);
    } else return bank;
  },
});
