'use strict';
const { genRand, convertPhone11To10 } = require('../../../utils/utils');
const fetch = require('node-fetch');
const dayjs = require('dayjs');
const { PhoneRegex } = require('../../../utils/constants');

/**
 * send-otp service
 */

module.exports = () => ({
  send: async (body) => {
    try {
      const phone = convertPhone11To10(body.phone);
      if (!PhoneRegex.test(phone)) {
        return { success: false, message: 'Phone number is invalid' };
      }

      // check phone existed
      const conflictingUserCount = await strapi
        .query('plugin::users-permissions.user')
        .count({
          where: {
            $or: [{ phone: phone.toLowerCase() }],
          },
        });
      if (conflictingUserCount > 0) {
        return { success: false, message: 'Phone is already taken' };
      }

      const otp = genRand();
      const otpBody = {
        oa_id: '1650355290320130813',
        template_id: 282220,
        phone,
        tracking_id: `${dayjs().unix()}_${phone}`,
        template_data: {
          otp,
        },
      };
      const sendOtp = `https://api.etelecom.vn/v1/shop.Zalo/SendZNS`;
      const response = await fetch(sendOtp, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization:
            'Bearer 1307576680433700980:8MBgMpZ9p91CkQNqDlzylAlL78DSCcsz',
        },
        body: JSON.stringify(otpBody),
      });
      const responseJson = await response.json();
      if (responseJson.id) {
        await strapi.entityService.create('api::otp.otp', {
          data: {
            phone,
            type: 'register',
            otp,
            confirm: false,
          },
        });
        return { success: true, message: 'Success' };
      } else {
        return {
          success: false,
          message: 'Something went wrong, check ZNS API',
        };
      }
    } catch (e) {
      console.log('🚀 ~ file: send-otp.js:40 ~ send: ~ e:', e);
    }
  },
  verify: async (body) => {
    try {
      const { otp, phone } = body;
      const data = await strapi.db.query('api::otp.otp').findOne({
        where: {
          phone,
          otp,
          type: 'register',
          confirm: false,
          createdAt: { $gt: dayjs().subtract(5, 'minute').toDate() },
        },
      });
      if (data) {
        await strapi.entityService.update('api::otp.otp', data.id, {
          data: {
            confirm: true,
          },
        });
        return { success: true };
      } else {
        return { success: false };
      }
    } catch (e) {
      return { success: false, message: e.message };
    }
  },
  forgot: async (body) => {
    try {
      const phone1 = body.phone;
      const p = phone1.includes('-') ? phone1.split('-')[0] : phone1;
      const phone = convertPhone11To10(p);
      if (!PhoneRegex.test(phone)) {
        return { success: false, message: 'Phone number is invalid' };
      }

      // check phone existed
      const user = await strapi
        .query('plugin::users-permissions.user')
        .findOne({ where: { phone } });

      if (!user) {
        return { success: false, message: 'Phone number does not exist' };
      }

      const otp = genRand();
      const otpBody = {
        oa_id: '1650355290320130813',
        template_id: 282220,
        phone,
        tracking_id: `${dayjs().unix()}_${phone}`,
        template_data: {
          otp,
        },
      };
      const sendOtp = `https://api.etelecom.vn/v1/shop.Zalo/SendZNS`;
      const response = await fetch(sendOtp, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization:
            'Bearer 1307576680433700980:8MBgMpZ9p91CkQNqDlzylAlL78DSCcsz',
        },
        body: JSON.stringify(otpBody),
      });
      const responseJson = await response.json();
      if (responseJson.id) {
        await strapi.entityService.create('api::otp.otp', {
          data: {
            phone,
            type: 'forgot',
            otp,
            confirm: false,
          },
        });
        return { success: true, message: 'Success' };
      } else {
        return {
          success: false,
          message: 'Something went wrong, check ZNS API',
        };
      }
    } catch (e) {
      console.log('🚀 ~ file: send-otp.js:40 ~ send: ~ e:', e);
    }
  },
  verifyForgot: async (body) => {
    try {
      const { otp, password } = body;
      const username = body.phone;
      const p = username.includes('-') ? username.split('-')[0] : username;
      const phone = convertPhone11To10(p);
      const data = await strapi.db.query('api::otp.otp').findOne({
        where: {
          phone,
          otp,
          type: 'forgot',
          confirm: false,
          createdAt: { $gt: dayjs().subtract(5, 'minute').toDate() },
        },
      });
      if (data) {
        if (password.length < 6) {
          return { success: false, message: 'Password is not strong enough' };
        }
        const u = await strapi.entityService.findMany(
          'plugin::users-permissions.user',
          { filters: { phone } }
        );
        await strapi.entityService.update(
          'plugin::users-permissions.user',
          u[0].id,
          { data: { password } }
        );
        await strapi.entityService.update('api::otp.otp', data.id, {
          data: {
            confirm: true,
          },
        });
        return { success: true };
      } else {
        return { success: false };
      }
    } catch (e) {
      console.log('🚀 ~ file: send-otp.js:182 ~ verifyForgot: ~ e:', e);
      return { success: false, message: e.message };
    }
  },
});
