'use strict';

/**
 * A set of functions called "actions" for `my-order`
 */

module.exports = {
  find: async (ctx, next) => {
    try {
      const data = await strapi
        .service('api::my-order.my-order')
        .find(ctx.state.user.id, ctx.query);
      ctx.body = data;
    } catch (err) {
      ctx.badRequest('Post report controller error', { moreDetails: err });
    }
  },
  findOne: async (ctx, next) => {
    try {
      const data = await strapi
        .service('api::my-order.my-order')
        .findOne(ctx.state.user.id, ctx.query, ctx.params);
      ctx.body = data;
    } catch (err) {
      ctx.badRequest('Post report controller error', { moreDetails: err });
    }
  },
  create: async (ctx, next) => {
    try {
      const data = await strapi
        .service('api::my-order.my-order')
        .create(ctx.request.body, ctx.state.user);
      ctx.body = data;
    } catch (err) {
      ctx.badRequest('Post report controller error', { moreDetails: err });
    }
  },
  cancel: async (ctx, next) => {
    try {
      const data = await strapi
        .service('api::my-order.my-order')
        .cancel(ctx.request.body, ctx.state.user);
      ctx.body = data;
    } catch (err) {
      ctx.badRequest('Post report controller error', { moreDetails: err });
    }
  },
  ship: async (ctx, next) => {
    try {
      const data = await strapi
        .service('api::my-order.my-order')
        .ship(ctx.request.body);
      ctx.body = data;
    } catch (err) {
      ctx.badRequest('Post report controller error', { moreDetails: err });
    }
  },
  createMac: async (ctx, next) => {
    try {
      const data = await strapi
        .service('api::my-order.my-order')
        .createMac(ctx.request.body);
      ctx.body = data;
    } catch (err) {
      ctx.badRequest('Post report controller error', { moreDetails: err });
    }
  },
  zaloNotify: async (ctx, next) => {
    try {
      const data = await strapi
        .service('api::my-order.my-order')
        .zaloNotify(ctx.request.body);
      ctx.body = data;
    } catch (err) {
      ctx.badRequest('Post report controller error', { moreDetails: err });
    }
  },
  checkoutCallback: async (ctx, next) => {
    try {
      const data = await strapi
        .service('api::my-order.my-order')
        .checkoutCallback(ctx.request.body);
      ctx.body = data;
    } catch (err) {
      ctx.badRequest('Post report controller error', { moreDetails: err });
    }
  },
};
