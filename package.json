{"name": "oferify-strapi", "private": true, "version": "0.1.0", "description": "A Strapi application", "scripts": {"install-lib": "cd src/plugins/management && yarn && cd ../../.. && yarn install", "develop": "npm rebuild sharp && strapi develop", "start": "strapi start", "build": "cd src/plugins/manage-user-tree && yarn && cd ../../.. && cd src/plugins/manage-order && yarn && cd ../../.. && cd src/plugins/dashboard && yarn && cd ../../.. && cd src/plugins/manage-promotion && yarn && cd ../../.. && strapi build", "strapi": "strapi", "postinstall": "patch-package", "watch-admin": "npm rebuild sharp && yarn develop --watch-admin", "dev-web": "cd ../oferify-web && npm run dev", "strapi-generate": "npx strapi generate"}, "devDependencies": {}, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/material": "^5.15.18", "@mui/x-tree-view": "^7.5.1", "@strapi/plugin-i18n": "4.12.5", "@strapi/plugin-users-permissions": "4.12.5", "@strapi/strapi": "4.12.5", "axios": "^1.6.8", "crypto-js": "^4.2.0", "dayjs": "^1.11.9", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "jszip": "^3.10.1", "koa2-ratelimit": "^1.1.3", "lodash": "^4.17.21", "nodemailer": "^6.9.7", "patch-package": "^8.0.0", "pg": "^8.11.3", "react-apexcharts": "^1.4.1", "react-d3-tree": "^3.6.2", "react-datepicker": "^4.18.0", "xlsx": "^0.18.5"}, "author": {"name": "A Strapi developer"}, "strapi": {"uuid": "960eac7b-4b4e-4512-967e-71832b938f9a"}, "engines": {"node": ">=16.0.0 <=20.x.x", "npm": ">=6.0.0"}, "license": "MIT", "prettier": {"singleQuote": true}}