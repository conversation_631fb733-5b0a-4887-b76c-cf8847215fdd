module.exports = {
  routes: [
    {
      method: 'GET',
      path: '/address-config/province',
      handler: 'address-config.getProvince',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/address-config/district',
      handler: 'address-config.getDistrict',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/address-config/ward',
      handler: 'address-config.getWard',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/address-config/bank',
      handler: 'address-config.getBank',
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
