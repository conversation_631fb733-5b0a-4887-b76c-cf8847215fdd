'use strict';
const utils = require('@strapi/utils');
const { ApplicationError, ValidationError } = utils.errors;

/**
 * my-parent service
 */

module.exports = () => ({
  find: async (user) => {
    try {
      const { fParent } = user;
      if (!fParent) {
        throw new ApplicationError('You do not have a parent yet');
      }
      const parent = await strapi.db
        .query('plugin::users-permissions.user')
        .findOne({
          where: { id: fParent },
          select: ['id', 'name', 'referCode'],
        });
      if (!parent) {
        throw new ApplicationError('Parent not found');
      }
      return { data: parent };
    } catch (err) {
      return err;
    }
  },
});
