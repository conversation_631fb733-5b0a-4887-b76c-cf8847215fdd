'use strict';

/**
 * transaction-payment router
 */

const { createCoreRouter } = require('@strapi/strapi').factories;

const defaultRouter = createCoreRouter('api::transaction-payment.transaction-payment')
const customRouter = (innerRouter, extraRoutes = []) => {
  let routes;
  return {
    get prefix() {
      return innerRouter.prefix;
    },
    get routes() {
      if (!routes) routes = innerRouter.routes.concat(extraRoutes);
      return routes;
    },
  };
};

const myExtraRoutes = [
  {
    method: "POST",
    path: "/create-payment",
    handler: "api::transaction-payment.transaction-payment.createPayment",
  },
];

module.exports = customRouter( defaultRouter , myExtraRoutes);
