{"kind": "collectionType", "collectionName": "terms", "info": {"singularName": "term", "pluralName": "terms", "displayName": "Term", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string", "required": true}, "content": {"type": "customField", "options": {"preset": "toolbar"}, "customField": "plugin::ckeditor5.CKEditor"}, "isCollaboratorPolicy": {"type": "boolean", "default": false}}}