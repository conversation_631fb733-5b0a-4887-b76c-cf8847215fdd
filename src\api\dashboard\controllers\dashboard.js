'use strict';

/**
 * A set of functions called "actions" for `dashboard`
 */

module.exports = {
  summary: async (ctx, next) => {
    try {
      const data = await strapi
        .service('api::dashboard.dashboard')
        .summary(ctx.state.user, ctx.query, ctx.params);
      ctx.body = data;
    } catch (err) {
      ctx.badRequest('Post report controller error', { moreDetails: err });
    }
  },
  summaryBusiness: async (ctx, next) => {
    try {
      const data = await strapi
        .service('api::dashboard.dashboard')
        .summaryBusiness(ctx.state.user, ctx.query, ctx.params);
      ctx.body = data;
    } catch (err) {
      ctx.badRequest('Post report controller error', { moreDetails: err });
    }
  },
  ranking: async (ctx, next) => {
    try {
      const data = await strapi.service('api::dashboard.dashboard').ranking();
      ctx.body = data;
    } catch (err) {
      ctx.badRequest('Post report controller error', { moreDetails: err });
    }
  },
};
