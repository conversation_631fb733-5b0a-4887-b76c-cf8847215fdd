'use strict';

const { createCoreService } = require('@strapi/strapi').factories;

module.exports = createCoreService('api::course.course', ({ strapi }) => ({
  async getListCourses({ page = 1, pageSize = 10, filters, user }) {
    const start = (page - 1) * pageSize;
    const limit = pageSize;

    let courseFilters = { ...filters };

    if (user) {
      // Lấy danh sách ID các khóa học user đã mua
      const purchasedCourseIds = await strapi.entityService
        .findMany('api::course.course', {
          filters: { users: { id: user.id } },
          fields: ['id'],
        })
        .then((courses) => courses.map((course) => course.id));

      // Chỉ lấy khóa học chưa mua nếu có user
      courseFilters.id = { $notIn: purchasedCourseIds };
    }

    // Lấy danh sách khóa học
    const [courses, total] = await Promise.all([
      strapi.entityService.findMany('api::course.course', {
        filters: courseFilters,
        populate: ['course_cat', 'image'],
        start,
        limit,
        sort: { id: 'desc' },
      }),
      strapi.entityService.count('api::course.course', {
        filters: courseFilters,
      }),
    ]);

    return {
      data: courses,
      meta: {
        page: Number(page),
        pageSize: Number(pageSize),
        total,
        totalPages: Math.ceil(total / pageSize),
      },
    };
  },

  async getCourseWithLessons(courseId, user) {
    // Lấy khóa học và danh sách bài học (KHÔNG populate users)
    const course = await strapi.entityService.findOne(
      'api::course.course',
      courseId,
      {
        populate: {
          image: true,
          lessons: {
            populate: ['main_video'],
          },
        },
      }
    );

    if (!course) return null;

    // Kiểm tra nếu khóa học có giá = 0
    const isFreeCourse = course.price === 0;

    // Nếu có user, kiểm tra xem user đã mua khóa học chưa
    const hasAccess = user
      ? await strapi.entityService.count('api::course.course', {
          filters: {
            id: courseId,
            users: { id: user.id },
          },
        })
      : 0;

    // Nếu khóa học không miễn phí và user chưa mua, ẩn `main_video`
    if (!isFreeCourse && !hasAccess) {
      course.lessons = course.lessons.map((lesson) => ({
        ...lesson,
        main_video: null,
      }));
      course.isBuyed = false;
    } else {
      course.isBuyed = true;
    }

    return course;
  },

  async getMyCourse(userId, { page = 1, pageSize = 10 }) {
    if (!userId) return { data: [], meta: {} };

    const start = (page - 1) * pageSize;
    const limit = pageSize;

    // Lấy danh sách khóa học mà user đã mua
    const [courses, total] = await Promise.all([
      strapi.entityService.findMany('api::course.course', {
        filters: { users: { id: userId } },
        populate: ['course_cat', 'image'],
        start,
        limit,
        sort: { id: 'desc' },
      }),
      strapi.entityService.count('api::course.course', {
        filters: { users: { id: userId } },
      }),
    ]);

    return {
      data: courses,
      meta: {
        page: Number(page),
        pageSize: Number(pageSize),
        total,
        totalPages: Math.ceil(total / pageSize),
      },
    };
  },

  async enrollFreeCourse(userId, courseId) {
    // Kiểm tra khóa học có tồn tại không
    const course = await strapi.entityService.findOne(
      'api::course.course',
      courseId
    );
    if (!course) {
      return;
    }

    // Chỉ cho phép đăng ký khóa học miễn phí
    if (course.price > 0) {
      return;
    }

    // Kiểm tra nếu user đã đăng ký khóa học này trước đó
    const alreadyEnrolled = await strapi.entityService.count(
      'api::course.course',
      {
        filters: {
          id: courseId,
          users: { id: userId },
        },
      }
    );

    if (alreadyEnrolled) {
      return;
    }

    // Thêm user vào khóa học miễn phí
    await strapi.entityService.update('api::course.course', courseId, {
      data: {
        users: {
          connect: [{ id: userId }],
        },
      },
    });

    return;
  },
}));
