{"kind": "collectionType", "collectionName": "articles", "info": {"singularName": "article", "pluralName": "articles", "displayName": "Article", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string"}, "description": {"type": "text"}, "image": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "article_cats": {"type": "relation", "relation": "manyToMany", "target": "api::article-cat.article-cat", "inversedBy": "articles"}, "content": {"type": "customField", "options": {"preset": "toolbar"}, "customField": "plugin::ckeditor5.CKEditor"}, "author": {"type": "string", "required": false}, "isBanner": {"type": "boolean", "default": false}, "type": {"type": "enumeration", "enum": ["home_banner", "collab_banner"], "required": true}}}