'use strict';
const fs = require('fs');
const path = require('path');
const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::course.course', ({ strapi }) => ({
  async find(ctx) {
    const { page = 1, pageSize = 10, search, category, isFeatured } = ctx.query;
    const user = ctx.state.user;
    const filters = {};

    if (search) {
      filters.name = { $containsi: search };
    }

    if (category) {
      filters.course_cat = category;
    }

    if (isFeatured !== undefined) {
      filters.isFeatured = isFeatured === 'true';
    }

    const courses = await strapi.service('api::course.course').getListCourses({
      page,
      pageSize,
      filters,
      user,
    });

    return ctx.send(courses);
  },

  async getOne(ctx) {
    const { id } = ctx.params;
    const user = ctx.state.user; // Lấy thông tin user từ request

    // Gọi service để lấy thông tin khóa học
    const course = await strapi
      .service('api::course.course')
      .getCourseWithLessons(id, user);

    if (!course) {
      return ctx.notFound('Course not found');
    }

    return ctx.send(course);
  },

  async getMyCourse(ctx) {
    const user = ctx.state.user;
    if (!user) {
      return ctx.unauthorized('User not authenticated');
    }

    const { page = 1, pageSize = 10 } = ctx.query;

    const courses = await strapi
      .service('api::course.course')
      .getMyCourse(user.id, { page, pageSize });

    return ctx.send(courses);
  },

  async enrollFreeCourse(ctx) {
    const { id } = ctx.request.query;
    const user = ctx.state.user;
    if (!user) {
      return;
    }
    try {
      const response = await strapi
        .service('api::course.course')
        .enrollFreeCourse(user.id, id);
      return ctx.send(response);
    } catch (error) {
      return ctx.badRequest(error.message);
    }
  },
}));
